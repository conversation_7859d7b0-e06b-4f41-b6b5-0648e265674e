import React from 'react';
// Correctly import modular form components
import { InputField } from './form/InputField';
import { RadioGroup } from './form/RadioGroup';
import { TextareaField } from './form/TextareaField';

// Import icons
import { User, Mail, Briefcase, MessageSquare, DollarSign, Type } from 'lucide-react';

// Define websiteTypeOptions with min and max prices for validation reference
// This array might also be defined in OrderPage.jsx or a shared constants file
// to be accessible by the validation logic. For display here, it's fine.
export const websiteTypeOptionsWithPricing = [ // Exporting if needed by OrderPage
  {
    value: 'ecommerce',
    label: '🛒 E-commerce Store',
    description: 'Est: $2,999 - $4,999',
    minPrice: 2999,
    maxPrice: 4999,
  },
  {
    value: 'personal',
    label: '👤 Personal Site',
    description: 'Est: $1,999 - $3,999',
    minPrice: 1999,
    maxPrice: 3999,
  },
  {
    value: 'ai_website',
    label: '🤖 AI-Powered Website',
    description: 'Est: $4,999 - $9,999',
    minPrice: 4999,
    maxPrice: 9999,
  },
  {
    value: 'custom',
    label: '✨ Custom Solution',
    description: 'We will contact you for pricing',
    // No min/max for custom as budget field won't show or isn't strictly validated against a range here
  },
];

const Step1_ProjectBasics = ({ formData, handleChange, errors }) => {
  const selectedType = websiteTypeOptionsWithPricing.find(
    (option) => option.value === formData.websiteType
  );
  const showBudgetField = selectedType && selectedType.value !== 'custom';

  let budgetPlaceholder = "e.g., 3000";
  let budgetHelpText = "Enter your estimated budget amount.";

  if (showBudgetField && selectedType && selectedType.minPrice && selectedType.maxPrice) {
    budgetPlaceholder = `e.g., ${selectedType.minPrice}`;
    budgetHelpText = `Must be between $${selectedType.minPrice.toLocaleString()} and $${selectedType.maxPrice.toLocaleString()}.`;
  }


  return (
    <div className="space-y-6 md:space-y-8">
      <div className="text-center md:text-left">
        <h2 className="text-2xl md:text-3xl font-bold text-slate-800 mb-1">🥇 Step 1: Project Basics</h2>
        <p className="text-sm md:text-base text-slate-600">
          Let's start with key information to understand your project needs.
        </p>
      </div>

      <InputField
        label="Full Name"
        name="fullName"
        value={formData.fullName || ''}
        onChange={handleChange}
        placeholder="e.g., Jane Doe"
        error={errors.fullName}
        icon={User}
      />
      <InputField
        label="Email Address"
        name="email"
        type="email"
        value={formData.email || ''}
        onChange={handleChange}
        placeholder="<EMAIL>"
        error={errors.email}
        icon={Mail}
      />
      <InputField
        label="Business Name"
        name="businessName"
        value={formData.businessName || ''}
        onChange={handleChange}
        placeholder="Your Company Inc."
        optional
        icon={Briefcase}
      />
      {/* Phone Number InputField REMOVED */}

      <RadioGroup
        label="What type of website do you need?"
        name="websiteType"
        options={websiteTypeOptionsWithPricing} // Use options with pricing data
        value={formData.websiteType || ''}
        onChange={handleChange}
        error={errors.websiteType}
        helpText="Select the primary category. Prices are estimates."
      />

      {showBudgetField && (
        <InputField
          label="Your Estimated Budget for This Project"
          name="projectBudget"
          type="text" // Keep as text to allow '$' or ',', will parse in validation
          value={formData.projectBudget || ''}
          onChange={handleChange}
          placeholder={budgetPlaceholder}
          error={errors.projectBudget}
          icon={DollarSign}
          helpText={budgetHelpText}
        />
      )}

      <TextareaField
        label="Describe your website project"
        name="websiteDescription"
        value={formData.websiteDescription || ''}
        onChange={handleChange}
        placeholder="Briefly describe its purpose, main features, target audience..."
        error={errors.websiteDescription}
        rows={5}
        icon={MessageSquare}
        helpText="The more details, the better we can tailor the solution."
      />
    </div>
  );
};

export default Step1_ProjectBasics;