import React from 'react';
import { CheckCircle } from 'lucide-react'; // Icon used directly in this component

export const DisplayUploadedFile = ({ label, fileName, icon: IconComponent }) => (
    <div className="py-2 sm:grid sm:grid-cols-3 sm:gap-4 items-start">
        <dt className="text-sm font-medium text-slate-500 flex items-center">
            {IconComponent && <IconComponent size={16} className="mr-2 text-slate-400 flex-shrink-0"/>}
            {label}:
        </dt>
        <dd className="mt-1 text-sm text-slate-900 sm:mt-0 sm:col-span-2 break-words">
            {fileName ? (
                <span className="flex items-center">
                    <CheckCircle size={14} className="mr-2 text-green-500 flex-shrink-0" />
                    {fileName}
                </span>
            ) : (
                <span className="italic text-slate-500">No file uploaded</span>
            )}
        </dd>
    </div>
);

export default DisplayUploadedFile; // Also adding a default export