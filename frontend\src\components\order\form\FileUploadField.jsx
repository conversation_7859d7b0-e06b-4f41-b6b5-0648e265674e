import React, { useState } from 'react';
import { UploadCloud, X, Check, AlertCircle } from 'lucide-react';

export const FileUploadField = ({ 
  label, 
  name, 
  fileName, // Display name of the uploaded file
  onChange, // Function to call when file changes: (fieldName, fileObject) => {}
  helpText, 
  error, 
  disabled, 
  maxSize = "10MB" 
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false); // Local state for upload simulation

  const handleFileChange = (e) => {
    if (disabled) return;
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setIsUploading(true);
      // Simulate upload delay for visual feedback, then call parent's onChange
      setTimeout(() => {
        onChange(name, file); // Pass field name and file object
        setIsUploading(false);
      }, 1000);
    }
     // Reset input value to allow re-uploading the same file
     e.target.value = null;
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    if (!disabled) setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);
    if (!disabled && e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      setIsUploading(true);
      setTimeout(() => {
        onChange(name, file);
        setIsUploading(false);
      }, 1000);
    }
  };

  const handleRemove = () => {
    if (disabled) return;
    onChange(name, null); // Signal removal to parent
  };

  return (
    <div className="space-y-2">
      {label && <label className="text-sm font-semibold text-slate-700">{label}</label>}
      
      <div
        className={`
          relative border-2 border-dashed rounded-2xl p-8 transition-all duration-300 group
          ${disabled 
            ? 'bg-slate-50 border-slate-200 cursor-not-allowed opacity-60' 
            : isDragOver 
              ? 'border-blue-400 bg-blue-50 scale-105 shadow-lg' 
              : fileName 
                ? 'border-green-400 bg-green-50' 
                : error
                  ? 'border-red-400 bg-red-50 hover:border-red-500'
                  : 'border-slate-300 bg-slate-50 hover:border-slate-400 hover:bg-slate-100'
          }
          ${!disabled ? 'cursor-pointer' : ''}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => !disabled && document.getElementById(name)?.click()} // Trigger file input click
      >
        <input
          id={name}
          name={name} // HTML name attribute
          type="file"
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer sr-only" // sr-only for accessibility
          onChange={handleFileChange}
          disabled={disabled}
        />

        {isUploading ? (
          <div className="text-center">
            <div className="animate-spin w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-sm font-medium text-blue-600">Uploading your file...</p>
            <p className="text-xs text-slate-500 mt-1">Please wait a moment</p>
          </div>
        ) : fileName ? (
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Check className="w-8 h-8 text-green-600" />
            </div>
            <p className="text-sm font-semibold text-green-700 mb-1 truncate max-w-full px-4">
              {fileName}
            </p>
            <p className="text-xs text-green-600 mb-4">File uploaded successfully</p>
            {!disabled && (
              <button
                type="button"
                onClick={(e) => { e.stopPropagation(); handleRemove(); }} // Prevent click propagation to div
                className="inline-flex items-center px-4 py-2 bg-white border border-slate-300 rounded-lg text-sm font-medium text-slate-600 hover:bg-slate-50 hover:border-slate-400 transition-all duration-200 shadow-sm"
              >
                <X className="w-4 h-4 mr-1" />
                Remove File
              </button>
            )}
          </div>
        ) : (
          <div className="text-center pointer-events-none"> {/* Prevent clicks on text when outer div is clickable */}
            <div className={`
              w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 transition-all duration-300
              ${isDragOver 
                ? 'bg-blue-100 scale-110' 
                : 'bg-slate-100 group-hover:bg-slate-200'
              }
            `}>
              <UploadCloud className={`w-8 h-8 ${isDragOver ? 'text-blue-600' : 'text-slate-500'}`} />
            </div>
            <p className="text-sm font-medium text-slate-700 mb-1">
              {isDragOver ? 'Drop your file here' : 'Click to upload or drag and drop'}
            </p>
            <p className="text-xs text-slate-500 mb-4">
              {helpText || `Max file size: ${maxSize}`}
            </p>
            <div className="inline-flex items-center px-4 py-2 bg-white border border-slate-300 rounded-lg text-sm font-medium text-slate-600 group-hover:bg-slate-50 transition-colors duration-200">
              Choose File
            </div>
          </div>
        )}
      </div>
      {error && (
        <div className="mt-2 flex items-center text-red-600 text-sm">
          <AlertCircle className="w-4 h-4 mr-1" />
          {error}
        </div>
      )}
    </div>
  );
};