import React, { useState } from 'react';
// Import the individual components from their new files
import { InputField } from './InputField';
import { RadioGroup } from './RadioGroup';
import { CheckboxGroup } from './CheckboxGroup';
import { FileUploadField } from './FileUploadField';

import { User, Mail, Lock } from 'lucide-react'; // Icons for the demo

const FormElements = () => {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    password: '',
    chosenPlan: '', // Renamed to avoid conflict with 'plan' if used as a prop name
    selectedFeatures: [], // Renamed for clarity
    profilePicture: null // To store the File object
  });
  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const handleRadioChange = (e) => { // RadioGroup onChange gives the event
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const handleCheckboxChange = (e) => {
    const { value, checked } = e.target; // 'value' is the value of the checkbox clicked
    setFormData(prev => ({
      ...prev,
      selectedFeatures: checked
        ? [...prev.selectedFeatures, value]
        : prev.selectedFeatures.filter(f => f !== value)
    }));
    if (errors.selectedFeatures) { // Assuming a single error message for the group
        setErrors(prev => ({ ...prev, selectedFeatures: null }));
    }
  };

  const handleFileUpload = (fieldName, file) => { // FileUploadField onChange gives (fieldName, fileObject)
    setFormData(prev => ({ ...prev, [fieldName]: file }));
    if (errors[fieldName]) {
      setErrors(prev => ({ ...prev, [fieldName]: null }));
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const newErrors = {};
    if (!formData.fullName.trim()) newErrors.fullName = "Full name is required.";
    if (!formData.email.trim()) newErrors.email = "Email is required.";
    else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = "Email is invalid.";
    if (!formData.password) newErrors.password = "Password is required.";
    else if (formData.password.length < 8) newErrors.password = "Password must be at least 8 characters.";
    if (!formData.chosenPlan) newErrors.chosenPlan = "Please select a plan.";
    if (formData.selectedFeatures.length === 0) newErrors.selectedFeatures = "Select at least one feature.";
    if (!formData.profilePicture) newErrors.profilePicture = "Profile picture is required.";

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      console.log("Form Submitted:", formData);
      alert("Form submitted successfully! Check console for data.");
      // Optionally reset form:
      // setFormData({ fullName: '', email: '', password: '', chosenPlan: '', selectedFeatures: [], profilePicture: null });
      // setErrors({});
    } else {
      console.log("Validation Errors:", newErrors);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-100 to-sky-100 p-4 md:p-8">
      <form onSubmit={handleSubmit} className="max-w-3xl mx-auto">
        <div className="bg-white rounded-2xl shadow-xl p-6 md:p-10 space-y-8">
          <div className="text-center mb-6">
            <h1 className="text-3xl font-bold text-slate-800 mb-2">Enhanced Form Elements</h1>
            <p className="text-slate-600">A showcase of modern and accessible form components.</p>
          </div>

          <InputField
            label="Full Name"
            name="fullName" // Matches formData key
            value={formData.fullName}
            onChange={handleInputChange}
            placeholder="e.g., Jane Doe"
            helpText="Your full name as it should appear."
            error={errors.fullName}
            icon={User}
            optional
          />

          <InputField
            label="Email Address"
            name="email" // Matches formData key
            type="email"
            value={formData.email}
            onChange={handleInputChange}
            placeholder="<EMAIL>"
            error={errors.email}
            icon={Mail}
          />

          <InputField
            label="Password"
            name="password" // Matches formData key
            type="password"
            value={formData.password}
            onChange={handleInputChange}
            placeholder="Create a strong password"
            helpText="Minimum 8 characters, include numbers and symbols."
            error={errors.password}
            icon={Lock}
          />

          <RadioGroup
            label="Choose Your Plan"
            name="chosenPlan" // Matches formData key
            value={formData.chosenPlan}
            onChange={handleRadioChange}
            helpText="You can change your plan later."
            options={[
              { value: 'basic', label: 'Basic', description: '$10/month' },
              { value: 'pro', label: 'Pro', description: '$25/month, more features' },
              { value: 'enterprise', label: 'Enterprise', description: 'Custom pricing' }
            ]}
            error={errors.chosenPlan}
          />

          <CheckboxGroup
            label="Select Features"
            name="selectedFeatures" // Name for the group, individual checkbox names are their values
            selectedOptions={formData.selectedFeatures} // Matches formData key
            onChange={handleCheckboxChange} // Handles event from individual checkboxes
            helpText="Pick the features you need."
            options={[
              { value: 'analytics', label: 'Advanced Analytics', description: 'Track user behavior' },
              { value: 'api_access', label: 'API Access', description: 'Integrate with other services' },
              { value: 'priority_support', label: 'Priority Support', description: 'Faster help when you need it' },
              { value: 'custom_branding', label: 'Custom Branding', description: 'White-label your interface' }
            ]}
            error={errors.selectedFeatures} // Error for the group
          />

          <FileUploadField
            label="Upload Profile Picture"
            name="profilePicture" // Matches formData key
            fileName={formData.profilePicture?.name} // Display name from the File object
            onChange={handleFileUpload} // Custom handler (fieldName, file)
            helpText="PNG, JPG, or GIF. Max 5MB."
            error={errors.profilePicture}
            maxSize="5MB"
          />

          <div className="pt-6 border-t border-slate-200">
            <button
              type="submit"
              className="w-full bg-gradient-to-r from-blue-600 to-indigo-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-indigo-300/50 hover:from-blue-700 hover:to-indigo-800 transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-4 focus:ring-blue-300"
            >
              Create Account
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default FormElements;