import React, { useState, useEffect } from 'react';
import { ArrowRight, Phone, Mail, MessageCircle, Zap, CheckCircle, Clock, Shield, Rocket } from 'lucide-react';
import { Link } from 'react-router-dom';

const ServicesCTA = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [currentBenefit, setCurrentBenefit] = useState(0);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 300);
    return () => clearTimeout(timer);
  }, []);

  const benefits = [
    { icon: Clock, text: "Fast 24-Hour Delivery", color: "from-blue-500 to-cyan-500" },
    { icon: Shield, text: "Enterprise-Grade Security", color: "from-purple-500 to-pink-500" },
    { icon: Rocket, text: "Scalable Solutions", color: "from-green-500 to-teal-500" },
    { icon: CheckCircle, text: "100% Quality Guarantee", color: "from-orange-500 to-red-500" }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentBenefit((prev) => (prev + 1) % benefits.length);
    }, 2000);
    return () => clearInterval(interval);
  }, []);

  const contactMethods = [
    {
      icon: Phone,
      title: "Call Us Now",
      description: "Speak directly with our experts",
      action: "tel:+15551234567",
      label: "+****************",
      color: "from-blue-500 to-cyan-500",
      bgColor: "from-blue-50 to-cyan-50"
    },
    {
      icon: Mail,
      title: "Email Us",
      description: "Get a detailed project proposal",
      action: "/contact",
      label: "Send Message",
      color: "from-purple-500 to-pink-500",
      bgColor: "from-purple-50 to-pink-50"
    },
    {
      icon: MessageCircle,
      title: "Live Chat",
      description: "Instant support and consultation",
      action: "#",
      label: "Start Chat",
      color: "from-green-500 to-teal-500",
      bgColor: "from-green-50 to-teal-50"
    }
  ];

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-20 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-6xl mx-auto relative z-10">
        
        {/* Main CTA Section */}
        <div className={`text-center mb-16 ${isVisible ? 'animate-in slide-in-from-bottom-8 fade-in' : 'translate-y-8 opacity-0'}`} style={{ animationDuration: '800ms' }}>
          
          {/* Badge */}
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm text-white rounded-full font-semibold text-sm mb-6 border border-white/20">
            <Zap className="w-4 h-4" />
            <span>Ready to Get Started?</span>
          </div>

          {/* Main Heading */}
          <h2 className="text-4xl lg:text-6xl font-bold mb-6 text-white leading-tight">
            Let's Build Something
            <br />
            <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-teal-400 bg-clip-text text-transparent">
              Amazing Together
            </span>
          </h2>
          
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8">
            Transform your ideas into reality with our expert development team. 
            Get started today and see your project come to life faster than you imagined.
          </p>

          {/* Dynamic Benefits */}
          <div className="flex items-center justify-center gap-4 p-6 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 mb-8 max-w-md mx-auto">
            <div className={`p-3 rounded-xl bg-gradient-to-r ${benefits[currentBenefit].color}`}>
              {React.createElement(benefits[currentBenefit].icon, { className: "w-6 h-6 text-white" })}
            </div>
            <div className="text-left">
              <p className="font-semibold text-white">{benefits[currentBenefit].text}</p>
              <div className="flex gap-1 mt-1">
                {benefits.map((_, index) => (
                  <div
                    key={index}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${
                      index === currentBenefit ? 'bg-white w-6' : 'bg-white/40'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Primary CTA Button */}
          <Link
            to="/contact"
            className="group inline-flex items-center justify-center gap-3 px-10 py-5 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-2xl font-bold text-lg shadow-2xl hover:shadow-blue-500/25 transform hover:scale-105 transition-all duration-300 mb-4"
          >
            <span>Start Your Project Now</span>
            <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
          </Link>
          
          <p className="text-sm text-gray-400">Free consultation • No commitment required</p>
        </div>

        {/* Contact Methods */}
        <div className={`grid md:grid-cols-3 gap-8 mb-16 ${isVisible ? 'animate-in slide-in-from-bottom-8 fade-in' : 'translate-y-8 opacity-0'}`} style={{ animationDuration: '800ms', animationDelay: '200ms' }}>
          {contactMethods.map((method, index) => (
            <div
              key={index}
              className="group relative bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300 transform hover:-translate-y-2"
            >
              {/* Background Gradient */}
              <div className={`absolute inset-0 bg-gradient-to-br ${method.bgColor} opacity-0 group-hover:opacity-10 transition-opacity duration-300 rounded-2xl`}></div>
              
              <div className="relative z-10">
                <div className={`w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-r ${method.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                  <method.icon className="w-8 h-8 text-white" />
                </div>
                
                <h3 className="text-xl font-bold text-white mb-2">{method.title}</h3>
                <p className="text-gray-300 mb-4">{method.description}</p>
                
                {method.action.startsWith('tel:') ? (
                  <a
                    href={method.action}
                    className={`inline-flex items-center justify-center gap-2 w-full px-6 py-3 bg-gradient-to-r ${method.color} text-white rounded-xl font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300`}
                  >
                    <span>{method.label}</span>
                    <ArrowRight className="w-4 h-4" />
                  </a>
                ) : (
                  <Link
                    to={method.action}
                    className={`inline-flex items-center justify-center gap-2 w-full px-6 py-3 bg-gradient-to-r ${method.color} text-white rounded-xl font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300`}
                  >
                    <span>{method.label}</span>
                    <ArrowRight className="w-4 h-4" />
                  </Link>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Final Assurance */}
        <div className={`text-center ${isVisible ? 'animate-in slide-in-from-bottom-8 fade-in' : 'translate-y-8 opacity-0'}`} style={{ animationDuration: '800ms', animationDelay: '400ms' }}>
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <h3 className="text-2xl font-bold text-white mb-4">Why Choose Us?</h3>
            <div className="grid md:grid-cols-4 gap-6">
              {[
                { icon: Clock, text: "24-Hour Delivery" },
                { icon: Shield, text: "Secure & Reliable" },
                { icon: CheckCircle, text: "Quality Guaranteed" },
                { icon: Rocket, text: "Future-Ready Tech" }
              ].map((item, index) => (
                <div key={index} className="flex flex-col items-center gap-2">
                  <div className="p-3 bg-white/20 rounded-full">
                    <item.icon className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-sm text-gray-300 font-medium">{item.text}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesCTA;
