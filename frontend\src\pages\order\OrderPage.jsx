// src/OrderPage.jsx

import React, { useState } from 'react';

// Import Step Components (ensure paths are correct)
import StepIndicator from '../../components/order/StepIndicator';
import Step1_ProjectBasics, { websiteTypeOptionsWithPricing } from '../../components/order/Step1_ProjectBasics'; // Assuming websiteTypeOptionsWithPricing is exported from here or a shared constants file
import Step2_GoalsPreferences from '../../components/order/Step2_GoalsPreferences';
import UploadsStep from '../../components/order/upload/UploadsStep';
import ConfirmationStep from '../../components/order/confrime/ConfirmationStep';

// Import Icons
import { ChevronLeft, ChevronRight, Send } from 'lucide-react';

const TOTAL_STEPS = 4;

const OrderPage = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // Step 1 Fields
    fullName: '',
    email: '',
    businessName: '',
    // phoneNumber: '', // Removed
    websiteType: '',
    projectBudget: '',
    websiteDescription: '',

    // Step 2 Fields
    mainGoal: [], // Initialize as an empty array for checkboxes
    mainGoalOther: '',
    logoBranding: '',
    contentReady: '',

    // Step 3 Fields (Uploads)
    uploads: {
      logo: null, // Will store File object
      sampleSites: null,
      brandColors: null,
      documents: null,
    },

    // Step 4 Fields (Confirmation)
    agreedToTerms: false,
  });

  const [errors, setErrors] = useState({});

  // Helper function to parse budget string to number
  const parseBudget = (budgetString) => {
    if (!budgetString || typeof budgetString !== 'string') return null;
    const cleanedString = budgetString.replace(/\$|,/g, '').split('-')[0].trim();
    const number = parseFloat(cleanedString);
    return isNaN(number) ? null : number;
  };

  const validateStep = () => {
    const newErrors = {};
    let stepIsValid = true;

    // Clear previous errors for the current step's relevant fields
    // This helps avoid displaying stale error messages after user correction
    const fieldsToClearForStep = {
        1: ['fullName', 'email', 'websiteType', 'projectBudget', 'websiteDescription'],
        2: ['mainGoal', 'mainGoalOther', 'logoBranding', 'contentReady'],
        3: ['uploads'], // Or more specific like 'uploads.logo' if needed
        4: ['agreedToTerms']
    };

    if (fieldsToClearForStep[currentStep]) {
        fieldsToClearForStep[currentStep].forEach(field => {
            if (errors[field]) {
                // For nested errors like 'uploads.logo', this needs more specific handling
                // For now, assuming direct keys or simple 'uploads' object reset
                if (field === 'uploads' && errors.uploads) {
                    // No specific clearing logic here, individual upload errors are shown in UploadsStep
                } else if (errors[field]) {
                     newErrors[field] = null; // Mark for clearing
                }
            }
        });
    }


    // --- Step 1 Validation ---
    if (currentStep === 1) {
      if (!formData.fullName.trim()) {
        newErrors.fullName = 'Full name is required.';
        stepIsValid = false;
      }
      if (!formData.email.trim()) {
        newErrors.email = 'Email is required.';
        stepIsValid = false;
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        newErrors.email = 'Email is invalid.';
        stepIsValid = false;
      }
      if (!formData.websiteType) {
        newErrors.websiteType = 'Please select a website type.';
        stepIsValid = false;
      }

      const selectedTypeOption = websiteTypeOptionsWithPricing.find(opt => opt.value === formData.websiteType);
      const showBudgetField = selectedTypeOption && selectedTypeOption.value !== 'custom';

      if (showBudgetField) {
        if (!formData.projectBudget?.trim()) {
          newErrors.projectBudget = 'Please provide an estimated budget.';
          stepIsValid = false;
        } else {
          const budgetAmount = parseBudget(formData.projectBudget);
          if (budgetAmount === null) {
            newErrors.projectBudget = 'Please enter a valid budget amount (e.g., 3000 or $3000).';
            stepIsValid = false;
          } else if (selectedTypeOption && typeof selectedTypeOption.minPrice === 'number' && budgetAmount < selectedTypeOption.minPrice) {
            newErrors.projectBudget = `Your budget must be within $${selectedTypeOption.minPrice.toLocaleString()} and $${selectedTypeOption.maxPrice.toLocaleString()}.`;
            stepIsValid = false;
          } else if (selectedTypeOption && typeof selectedTypeOption.maxPrice === 'number' && budgetAmount > selectedTypeOption.maxPrice) {
            newErrors.projectBudget = `Your budget must be within $${selectedTypeOption.minPrice.toLocaleString()} and $${selectedTypeOption.maxPrice.toLocaleString()}.`;
            stepIsValid = false;
          }
        }
      }

      if (!formData.websiteDescription.trim()) {
        newErrors.websiteDescription = 'Please describe your website project.';
        stepIsValid = false;
      }
    }
    // --- Step 2 Validation ---
    else if (currentStep === 2) {
      if (!formData.mainGoal || formData.mainGoal.length === 0) {
        if (!formData.mainGoal.includes('other_goal')) {
          newErrors.mainGoal = 'Please select or specify your main goal.';
          stepIsValid = false;
        }
      }
      if (formData.mainGoal && formData.mainGoal.includes('other_goal') && (!formData.mainGoalOther || !formData.mainGoalOther.trim())) {
        newErrors.mainGoalOther = 'Please specify your "Other" goal.';
        stepIsValid = false;
      }
      if (!formData.logoBranding) {
        newErrors.logoBranding = 'Please select an option for logo & branding.';
        stepIsValid = false;
      }
      if (!formData.contentReady) {
        newErrors.contentReady = 'Please indicate if your content is ready.';
        stepIsValid = false;
      }
    }
    // --- Step 3 Validation (Uploads) ---
    // Typically optional, but you could add checks here if certain files are mandatory.
    // For example:
    // if (currentStep === 3 && !formData.uploads.logo) {
    //   newErrors.uploads = { ...newErrors.uploads, logo: "Logo upload is recommended." };
    //   // stepIsValid = false; // Only if it should block progress
    // }


    // --- Step 4 Validation (Confirmation) ---
    // `agreedToTerms` is specifically handled in `handleSubmit`.

    setErrors(prevErrors => {
        const updatedErrors = {...prevErrors};
        // Apply new errors or clear existing ones
        Object.keys(newErrors).forEach(key => {
            if (newErrors[key] === null) { // Null means clear this error
                delete updatedErrors[key];
            } else {
                updatedErrors[key] = newErrors[key];
            }
        });
        return updatedErrors;
    });

    return stepIsValid; // Return the overall validity of the current step's fields
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    // Optimistically clear error for the field being changed
    if (errors[name] || (name.startsWith('uploads.') && errors.uploads && errors.uploads[name.split('.')[1]])) {
        setErrors(prev => {
            const updatedErrors = { ...prev };
            if (name.startsWith('uploads.')) {
                const uploadField = name.split('.')[1];
                if (updatedErrors.uploads) {
                    delete updatedErrors.uploads[uploadField];
                    if (Object.keys(updatedErrors.uploads).length === 0) delete updatedErrors.uploads;
                }
            } else {
                delete updatedErrors[name];
            }
            return updatedErrors;
        });
    }


    if (name === "websiteType") {
      setFormData(prev => ({
        ...prev,
        websiteType: value,
        projectBudget: '', // Clear budget when type changes
      }));
      // Also clear projectBudget error when websiteType changes
      if (errors.projectBudget) {
        setErrors(prev => {
            const updatedErrors = { ...prev };
            delete updatedErrors.projectBudget;
            return updatedErrors;
        });
      }
      return; // Exit early because we've handled formData and potentially errors
    }

    if (type === 'checkbox') {
      if (name === "mainGoal") {
        setFormData((prev) => ({
          ...prev,
          mainGoal: checked
            ? [...(prev.mainGoal || []), value]
            : (prev.mainGoal || []).filter((item) => item !== value),
        }));
      } else { // Handles single boolean checkboxes like 'agreedToTerms'
        setFormData((prev) => ({ ...prev, [name]: checked }));
      }
    } else { // Handles text inputs, radio buttons, select, textarea
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleFileUpload = (fieldName, file) => {
    setFormData(prev => ({
      ...prev,
      uploads: {
        ...prev.uploads,
        [fieldName]: file // Stores the File object
      }
    }));
    // Clear specific upload error from a nested errors.uploads object
    if (errors.uploads && errors.uploads[fieldName]) {
      setErrors(prev => {
        const updatedUploadErrors = { ...prev.uploads };
        delete updatedUploadErrors[fieldName];
        return { ...prev, uploads: Object.keys(updatedUploadErrors).length > 0 ? updatedUploadErrors : undefined };
      });
    }
  };

  const nextStep = () => {
    if (validateStep()) { // validateStep() now returns true if current step fields are valid
      if (currentStep < TOTAL_STEPS) {
        setCurrentStep(currentStep + 1);
        // Errors for the *current* step were handled by validateStep.
        // Errors for *other* steps are preserved unless explicitly cleared.
      }
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      // Optionally, preserve errors for the step you are going back to,
      // or clear all errors for a fresh state on that step.
      // For simplicity, clearing all is often fine.
      setErrors({});
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // Final validation for agreedToTerms if on the last step
    if (currentStep === TOTAL_STEPS) {
      if (!formData.agreedToTerms) {
        setErrors(prev => ({ ...prev, agreedToTerms: 'You must agree to the terms and conditions.' }));
        return; // Stop submission
      }
      // If terms were an error but now are checked, clear the error
      if (errors.agreedToTerms && formData.agreedToTerms) {
        setErrors(prev => {
            const updatedErrors = {...prev};
            delete updatedErrors.agreedToTerms;
            return updatedErrors;
        });
      }
    }

    // Optional: Perform a full validation of all form data here if desired,
    // or rely on the step-by-step validation being sufficient.
    // For example, you could iterate through all steps and call validateStep logic for each.

    // If we've reached here and agreedToTerms is fine (if on last step), proceed with submission
    console.log('Form Submitted:', formData);
    alert('Order Request Submitted! We will get in touch with you shortly.');
    // Optionally reset form and state:
    // setCurrentStep(1);
    // setFormData({ /* initial empty state */ });
    // setErrors({});
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return <Step1_ProjectBasics formData={formData} handleChange={handleChange} errors={errors} />;
      case 2:
        return <Step2_GoalsPreferences formData={formData} handleChange={handleChange} errors={errors} />;
      case 3:
        return <UploadsStep
          formData={formData}
          handleFileUpload={handleFileUpload}
          // Pass down the 'uploads' part of errors, or specific error keys if your UploadsStep expects that
          errors={errors.uploads || {}}
        />;
      case 4:
        return <ConfirmationStep formData={formData} handleChange={handleChange} errors={errors} />;
      default:
        return <p>Loading step...</p>;
    }
  };

  const stepTitles = [
    "Project Basics",
    "Goals & Preferences",
    "Upload Assets",
    "Review & Confirm"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-100 via-sky-50 to-indigo-100 md:py-28 py-28 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto bg-white rounded-xl shadow-2xl overflow-hidden">
        <div className="p-6 md:p-10">
          <StepIndicator currentStep={currentStep} totalSteps={TOTAL_STEPS} titles={stepTitles} />
          <div className="mt-8 min-h-[450px] md:min-h-[500px]">
            <div key={currentStep} className="animate-fadeIn">
              {renderStep()}
            </div>
          </div>
        </div>

        <div className="bg-slate-50 px-6 py-4 md:px-10 md:py-6 border-t border-slate-200">
          <div className="flex justify-between items-center">
            <button
              onClick={prevStep}
              disabled={currentStep === 1}
              className="flex items-center gap-2 px-6 py-3 rounded-lg text-sm font-medium text-slate-700 bg-slate-200 hover:bg-slate-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-1"
            >
              <ChevronLeft size={18} />
              Previous
            </button>

            {currentStep < TOTAL_STEPS ? (
              <button
                onClick={nextStep}
                className="flex items-center gap-2 px-6 py-3 rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors duration-200 shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-1"
              >
                Next Step
                <ChevronRight size={18} />
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                className="flex items-center gap-2 px-6 py-3 rounded-lg text-sm font-medium text-white bg-green-600 hover:bg-green-700 transition-colors duration-200 shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-1"
              >
                <Send size={18} />
                Submit Order Request
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderPage;