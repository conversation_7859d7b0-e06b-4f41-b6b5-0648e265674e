import React, { useState } from 'react';
import { Plus, Minus } from 'lucide-react';

const FaqItem = ({ question, answer }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="bg-slate-50 rounded-lg shadow-sm mb-3">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex justify-between items-center w-full py-4 px-6 text-left text-slate-800 focus:outline-none"
      >
        <span className="font-medium text-base md:text-lg">{question}</span>
        {isOpen ? <Minus className="w-5 h-5 text-blue-600" /> : <Plus className="w-5 h-5 text-blue-600" />}
      </button>
      {isOpen && (
        <div className="pb-4 px-6 text-slate-600 text-sm md:text-base leading-relaxed">
          {answer}
        </div>
      )}
    </div>
  );
};

export default FaqItem;