import React from 'react';
import { Facebook, Linkedin, Twitter } from 'lucide-react'; // Or other relevant social icons

// Placeholder for Rio Logo (can be the same as used in ContactPage)
const RioLogoDarkBg = () => (
  <div className="flex items-center space-x-2">
    <div className="w-7 h-7 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xs">
      R
    </div>
    <span className="text-lg font-bold text-white">Rio</span>
  </div>
);

const CtaSection = () => {
  // A simple way to create a subtle dot pattern with CSS
  const dotPatternStyle = {
    backgroundImage: 'radial-gradient(circle, rgba(255,255,255,0.05) 1px, transparent 1.5px)',
    backgroundSize: '20px 20px',
  };

  return (
    <div className="bg-slate-900 text-white py-16 md:py-24" style={dotPatternStyle}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <span className="inline-block bg-slate-700 text-slate-300 px-3 py-1 rounded-full text-xs font-semibold uppercase tracking-wider mb-6">
          Testimonials
        </span>
        <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4">
          Ready to <span className="text-blue-500">Get Started?</span>
        </h2>
        <p className="text-slate-300 text-base md:text-lg max-w-2xl mx-auto mb-10 leading-relaxed">
          Experience the future of business operations with AI automation—increased
          speed, accuracy, and adaptability, driving overall productivity gains.
        </p>
        <a
          href="/free-audit-call" // Replace with your actual link for ordering or booking
          className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-4 rounded-lg shadow-lg transition-colors duration-300 transform hover:scale-105 text-base md:text-lg"
        >
          Book Your Free Audit Call
        </a>
      </div>

      {/* Mini Footer within this section */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 mt-16 md:mt-24 pt-8 border-t border-slate-700">
        <div className="flex flex-col md:flex-row justify-between items-center space-y-6 md:space-y-0">
          <div className="flex items-center space-x-4">
            <RioLogoDarkBg />
            <p className="text-slate-400 text-sm hidden sm:block">Build a powerful agency site with Framer.</p>
          </div>

          <div className="flex items-center space-x-6">
            <p className="text-slate-400 text-sm sm:hidden">Build a powerful agency site with Framer.</p>
            <nav className="flex space-x-6">
              <a href="/about" className="text-slate-300 hover:text-blue-400 text-sm transition-colors">About</a>
              <a href="/contact-page" className="text-slate-300 hover:text-blue-400 text-sm transition-colors">Contact</a> {/* Changed from /contact to avoid confusion if this is the contact page itself */}
              <a href="/blog" className="text-slate-300 hover:text-blue-400 text-sm transition-colors">Blog</a>
            </nav>
          </div>
        </div>
        <div className="flex justify-start items-center space-x-4 mt-6 md:mt-0 md:absolute md:bottom-8 md:left-[calc(50%-((3*2.5rem+2*1rem)/2))] md:transform md:-translate-x-1/2">
             {/* Social Icons - positioned more centrally on larger screens or adjust as needed */}
             <div className="flex items-center space-x-4 mt-6 md:mt-0 pt-6 md:pt-0"> {/* Moved social icons to be directly after RioLogo on small screens */}
                <a href="#" aria-label="Facebook" className="text-slate-400 hover:text-blue-400 transition-colors">
                    <Facebook className="w-5 h-5" />
                </a>
                <a href="#" aria-label="LinkedIn" className="text-slate-400 hover:text-blue-400 transition-colors">
                    <Linkedin className="w-5 h-5" />
                </a>
                <a href="#" aria-label="Twitter" className="text-slate-400 hover:text-blue-400 transition-colors">
                    <Twitter className="w-5 h-5" />
                </a>
            </div>
        </div>
      </div>
    </div>
  );
};

export default CtaSection;