import React, { useState } from 'react';
import { MapPin, Mail, Phone, Send, Loader2, User, BookText, MessageCircle } from 'lucide-react';
import FaqItem from '../../components/contact/FaqItem'; // Make sure the path is correct

// Placeholder for Rio Logo (replace with your actual SVG or image)
const RioLogo = () => (
  <div className="flex items-center space-x-2 mb-8">
    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
      C
    </div>
    <span className="text-xl font-bold text-slate-800">Chadoom</span>
  </div>
);

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus(null);
    // --- Replace with your actual form submission logic ---
    try {
      console.log("Form Data Submitted:", formData);
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSubmitStatus('success');
      setFormData({ name: '', email: '', message: '' });
    } catch (error) {
      console.error("Submission error:", error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
    // --- End of replacement section ---
  };

  const faqData = [
    {
      question: "How can I tell if using AI is the right solution for my problem?",
      answer: "We assess your current workflows, data availability, and specific challenges. If AI can provide significant improvements in efficiency, accuracy, or insights, and the ROI is clear, then it's likely a good fit. We offer a free consultation to explore this.",
    },
    {
      question: "How long does an AI solution take to implement?",
      answer: "Implementation time varies depending on complexity, data integration needs, and customization. Simple solutions might take a few weeks, while more complex, bespoke systems can take several months. We provide a detailed timeline after the initial assessment.",
    },
    {
      question: "Will we need to make changes in our teams?",
      answer: "Often, AI solutions augment existing teams rather than replace them. Some upskilling or new roles focused on managing and interpreting AI outputs might be beneficial. We provide training and support to ensure a smooth transition and empower your team to leverage the new technology effectively.",
    },
  ];

  return (
    <div className="bg-white min-h-screen py-12 md:py-20 px-4 sm:px-6 lg:px-8">
      {/* Get in Touch Section */}
      <div className="max-w-6xl mx-auto">
        <div className="grid md:grid-cols-12 gap-8 md:gap-16 items-start">
          {/* Left Column: Contact Info */}
          <div className="md:col-span-5 lg:col-span-4">
            <span className="inline-block bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-xs font-semibold uppercase tracking-wider mb-4">
              Contact
            </span>
            <h1 className="text-4xl md:text-5xl font-bold text-slate-800 mb-4">
              Get in Touch
            </h1>
            <p className="text-slate-600 text-lg leading-relaxed mb-8">
              We help you automate your workflows, automate repetitive tasks, and elevate your business.
            </p>

            <div className="space-y-6">
              <div className="flex items-start">
                <MapPin className="w-6 h-6 text-blue-600 mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-slate-700 text-lg">Our office</h3>
                  <p className="text-slate-500 text-sm">
                    1875 NewTown Ave 10th Floor, AW,
                    <br />
                    Washington, District of America
                  </p>
                </div>
              </div>
              <div className="flex items-start">
                {/* Using a generic message icon as in the image for "Get in touch" block */}
                <MessageCircle className="w-6 h-6 text-blue-600 mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-slate-700 text-lg">Get in touch</h3>
                  <a href="tel:+1234567890245" className="text-slate-500 hover:text-blue-600 transition-colors text-sm block">
                    +123 ************
                  </a>
                  <a href="mailto:<EMAIL>" className="text-slate-500 hover:text-blue-600 transition-colors text-sm block">
                    <EMAIL>
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column: Contact Form */}
          <div className="md:col-span-7 lg:col-span-8">
            <div className="bg-white p-8 md:p-10 rounded-xl shadow-2xl border border-slate-100">
              <RioLogo />
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  {/* Label removed, using placeholder as per image style */}
                  <input
                    type="text"
                    name="name"
                    id="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="mt-1 block w-full px-4 py-3 bg-slate-100 border-transparent rounded-md focus:border-blue-500 focus:ring-0 focus:outline-none sm:text-sm placeholder-slate-400 transition-colors"
                    placeholder="Name"
                    disabled={isSubmitting}
                  />
                </div>
                <div>
                  <input
                    type="email"
                    name="email"
                    id="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="mt-1 block w-full px-4 py-3 bg-slate-100 border-transparent rounded-md focus:border-blue-500 focus:ring-0 focus:outline-none sm:text-sm placeholder-slate-400 transition-colors"
                    placeholder="Email"
                    disabled={isSubmitting}
                  />
                </div>
                <div>
                  <textarea
                    id="message"
                    name="message"
                    rows="5"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    className="mt-1 block w-full px-4 py-3 bg-slate-100 border-transparent rounded-md focus:border-blue-500 focus:ring-0 focus:outline-none sm:text-sm placeholder-slate-400 transition-colors"
                    placeholder="Message"
                    disabled={isSubmitting}
                  ></textarea>
                </div>
                <div>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full flex items-center justify-center px-6 py-3.5 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all shadow-md hover:shadow-lg disabled:opacity-70 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="animate-spin w-5 h-5 mr-3" />
                        Sending...
                      </>
                    ) : (
                      "Submit" // Changed from Send Message to Submit
                    )}
                  </button>
                </div>
              </form>
              {submitStatus === 'success' && (
                <div className="mt-4 p-3 bg-green-50 border border-green-300 text-green-700 rounded-md text-center text-sm">
                  Message sent successfully! We'll be in touch soon.
                </div>
              )}
              {submitStatus === 'error' && (
                <div className="mt-4 p-3 bg-red-50 border border-red-300 text-red-700 rounded-md text-center text-sm">
                  Oops! Something went wrong. Please try again.
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="max-w-3xl mx-auto mt-20 md:mt-28">
        <div className="text-center mb-10 md:mb-12">
          <span className="inline-block bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-xs font-semibold uppercase tracking-wider mb-3">
            FAQ
          </span>
          <h2 className="text-3xl md:text-4xl font-bold text-slate-800">
            You've got questions,
            <br className="sm:hidden"/>
            <span className="text-blue-600"> we've got answers</span>
          </h2>
        </div>
        <div className="space-y-0"> {/* Adjusted space-y-0 as FaqItem has mb-3 */}
          {faqData.map((item, index) => (
            <FaqItem key={index} question={item.question} answer={item.answer} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ContactPage;