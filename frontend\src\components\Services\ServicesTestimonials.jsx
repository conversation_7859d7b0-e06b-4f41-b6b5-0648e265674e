import React, { useState, useEffect } from 'react';
import { Star, Quote, ChevronLeft, ChevronRight, Users } from 'lucide-react';

const ServicesTestimonials = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 300);
    return () => clearTimeout(timer);
  }, []);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      role: "CEO, TechStart Inc.",
      company: "TechStart Inc.",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
      rating: 5,
      text: "The team delivered our e-commerce platform ahead of schedule with exceptional quality. Their attention to detail and technical expertise exceeded our expectations. The mobile app they built has increased our sales by 300%.",
      project: "E-commerce Platform & Mobile App",
      deliveryTime: "3 weeks"
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "Founder, <PERSON><PERSON><PERSON><PERSON><PERSON>",
      company: "EduLearn",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
      rating: 5,
      text: "Outstanding work on our learning management system. The full-stack solution they provided handles thousands of students seamlessly. Their ongoing support has been invaluable for our growth.",
      project: "Learning Management System",
      deliveryTime: "4 weeks"
    },
    {
      id: 3,
      name: "Emily Rodriguez",
      role: "Marketing Director, HealthCare Plus",
      company: "HealthCare Plus",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
      rating: 5,
      text: "The healthcare management platform they built is incredibly robust and user-friendly. Patient satisfaction has improved significantly, and our staff loves the intuitive interface. Highly recommended!",
      project: "Healthcare Management Platform",
      deliveryTime: "6 weeks"
    },
    {
      id: 4,
      name: "David Thompson",
      role: "CTO, FinanceFlow",
      company: "FinanceFlow",
      image: "https://images.unsplash.com/photo-*************-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
      rating: 5,
      text: "Security and performance were our top priorities for our banking application. They delivered a solution that exceeds industry standards. The code quality and documentation are exceptional.",
      project: "Banking Application",
      deliveryTime: "8 weeks"
    },
    {
      id: 5,
      name: "Lisa Wang",
      role: "Product Manager, PropertyHub",
      company: "PropertyHub",
      image: "https://images.unsplash.com/photo-**********-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
      rating: 5,
      text: "Our real estate platform has transformed how we do business. The virtual tour integration and advanced search features have set us apart from competitors. Excellent work!",
      project: "Real Estate Marketplace",
      deliveryTime: "4 weeks"
    }
  ];

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  useEffect(() => {
    const interval = setInterval(nextTestimonial, 5000);
    return () => clearInterval(interval);
  }, []);

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-blue-50 to-indigo-100 relative overflow-hidden">
      {/* Background Decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-20 w-64 h-64 bg-blue-200/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-64 h-64 bg-purple-200/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="max-w-6xl mx-auto relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-100 text-blue-600 rounded-full font-semibold text-sm mb-4">
            <Users className="w-4 h-4" />
            <span>Client Testimonials</span>
          </div>
          
          <h2 className="text-4xl lg:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 bg-clip-text text-transparent leading-tight">
            What Our Clients Say
          </h2>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Don't just take our word for it. Here's what our satisfied clients have to say about our services.
          </p>
        </div>

        {/* Testimonial Slider */}
        <div className="relative">
          <div className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl border border-gray-100 overflow-hidden">
            <div className="p-8 lg:p-12">
              
              {/* Quote Icon */}
              <div className="flex justify-center mb-8">
                <div className="p-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full">
                  <Quote className="w-8 h-8 text-white" />
                </div>
              </div>

              {/* Testimonial Content */}
              <div className={`text-center transition-all duration-500 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
                
                {/* Rating */}
                <div className="flex justify-center gap-1 mb-6">
                  {[...Array(testimonials[currentTestimonial].rating)].map((_, index) => (
                    <Star key={index} className="w-6 h-6 text-yellow-500 fill-current" />
                  ))}
                </div>

                {/* Testimonial Text */}
                <blockquote className="text-xl lg:text-2xl text-gray-700 leading-relaxed mb-8 max-w-4xl mx-auto font-medium">
                  "{testimonials[currentTestimonial].text}"
                </blockquote>

                {/* Project Info */}
                <div className="inline-flex items-center gap-4 px-6 py-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-full border border-blue-100 mb-8">
                  <span className="text-sm font-semibold text-blue-600">Project:</span>
                  <span className="text-sm text-gray-700">{testimonials[currentTestimonial].project}</span>
                  <span className="text-sm text-gray-500">•</span>
                  <span className="text-sm text-gray-700">Delivered in {testimonials[currentTestimonial].deliveryTime}</span>
                </div>

                {/* Client Info */}
                <div className="flex items-center justify-center gap-4">
                  <img
                    src={testimonials[currentTestimonial].image}
                    alt={testimonials[currentTestimonial].name}
                    className="w-16 h-16 rounded-full object-cover border-4 border-white shadow-lg"
                  />
                  <div className="text-left">
                    <h4 className="text-lg font-bold text-gray-800">{testimonials[currentTestimonial].name}</h4>
                    <p className="text-gray-600">{testimonials[currentTestimonial].role}</p>
                    <p className="text-sm text-blue-600 font-semibold">{testimonials[currentTestimonial].company}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Buttons */}
          <button
            onClick={prevTestimonial}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 p-3 bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 hover:border-blue-300"
          >
            <ChevronLeft className="w-6 h-6 text-gray-600" />
          </button>
          
          <button
            onClick={nextTestimonial}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 p-3 bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 hover:border-blue-300"
          >
            <ChevronRight className="w-6 h-6 text-gray-600" />
          </button>
        </div>

        {/* Testimonial Indicators */}
        <div className="flex justify-center gap-3 mt-8">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentTestimonial(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                currentTestimonial === index
                  ? 'bg-blue-600 w-8'
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
            />
          ))}
        </div>

        {/* Stats Section */}
        <div className="grid md:grid-cols-3 gap-8 mt-16">
          <div className="text-center p-6 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-100">
            <div className="text-4xl font-bold text-blue-600 mb-2">500+</div>
            <div className="text-gray-600 font-semibold">Happy Clients</div>
          </div>
          
          <div className="text-center p-6 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-100">
            <div className="text-4xl font-bold text-purple-600 mb-2">98%</div>
            <div className="text-gray-600 font-semibold">Satisfaction Rate</div>
          </div>
          
          <div className="text-center p-6 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-100">
            <div className="text-4xl font-bold text-teal-600 mb-2">4.9/5</div>
            <div className="text-gray-600 font-semibold">Average Rating</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesTestimonials;
