import React from 'react';
import { 
  Zap, 
  Phone, 
  Mail, 
  MapPin, 
  Clock, 
  Facebook, 
  Twitter, 
  Instagram, 
  Linkedin, 
  Github,
  ArrowRight,
  CheckCircle,
  Star,
  Heart,
  Shield,
  Award
} from 'lucide-react';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  

  const legalLinks = [
    { name: 'Privacy Policy', href: '#privacy' },
    { name: 'Terms of Service', href: '#terms' },
    { name: 'Cookie Policy', href: '#cookies' },
    { name: 'GDPR Compliance', href: '#gdpr' }
  ];

  

  return (
    <footer className="bg-black/30  text-white relative overflow-hidden">
      
      
      {/* Bottom Bar */}
      <div className=" text-white border-t border-gray-700">
        <div className="container mx-auto px-6 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            
            {/* Copyright */}
            <div className="text-center md:text-left mb-4 md:mb-0">
              <p className="text-gray-100 text-sm">
                © {currentYear} PerfectSites. All rights reserved.
              </p>
              <p className=" text-xs mt-1 flex items-center justify-center md:justify-start">
                Made with <Heart className="w-3 h-3 text-red-500 mx-1" /> for amazing businesses
              </p>
            </div>

            {/* Legal Links */}
            <div className="flex flex-wrap justify-center gap-6">
              {legalLinks.map((link, index) => (
                <a
                  key={index}
                  href={link.href}
                  className="text-gray-100 hover:scale-110 duration-300 text-sm transition-colors "
                >
                  {link.name}
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}