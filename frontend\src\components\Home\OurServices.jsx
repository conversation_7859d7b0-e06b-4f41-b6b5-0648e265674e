import React, { useState, useEffect } from 'react';
import { ShoppingCart, User, Brain, Globe, ArrowRight, Star, Zap } from 'lucide-react';

const OurServices = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [hoveredCard, setHoveredCard] = useState(null);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 200);
    return () => clearTimeout(timer);
  }, []);

  const services = [
    {
      icon: ShoppingCart,
      title: "E-commerce",
      desc: "Complete online stores with secure payment integration and inventory management",
      features: ["Payment Gateway", "Inventory System", "Mobile Optimized"],
      color: "from-orange-500 to-red-500",
      bgColor: "from-orange-50 to-red-50", // Light colors for bg overlay
      hoverColor: "hover:shadow-orange-500/25",
      borderColor: "hover:border-orange-400/60",
      delay: "0ms",
      price: "Starting at $2,999"
    },
    {
      icon: User,
      title: "Personal Website",
      desc: "Stunning portfolios and personal brands that showcase your unique story",
      features: ["Custom Design", "SEO Optimized", "Fast Loading"],
      color: "from-blue-500 to-cyan-500",
      bgColor: "from-blue-50 to-cyan-50",
      hoverColor: "hover:shadow-blue-500/25",
      borderColor: "hover:border-blue-400/60",
      delay: "150ms",
      price: "Starting at $999"
    },
    {
      icon: Brain,
      title: "AI Website",
      desc: "Intelligent websites with AI capabilities and machine learning integration",
      features: ["AI Integration", "Smart Features", "Future-Ready"],
      color: "from-purple-500 to-pink-500",
      bgColor: "from-purple-50 to-pink-50",
      hoverColor: "hover:shadow-purple-500/25",
      borderColor: "hover:border-purple-400/60",
      delay: "300ms",
      price: "Starting at $4,999"
    },
    {
      icon: Globe,
      title: "Custom Solutions",
      desc: "Whatever you have in mind, we build it with cutting-edge technology",
      features: ["Unlimited Possibilities", "Latest Tech", "Full Support"],
      color: "from-green-500 to-teal-500",
      bgColor: "from-green-50 to-teal-50",
      hoverColor: "hover:shadow-green-500/25",
      borderColor: "hover:border-green-400/60",
      delay: "450ms",
      price: "Custom Quote"
    }
  ];

  return (
    <section className="py-4 px-4  relative overflow-hidden">
      {/* Background Decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-200/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-200/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-blue-100/10 to-purple-100/10 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        {/* Header */}
        <div className="text-center mb-20">       
          <h2 className="text-6xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 bg-clip-text text-transparent leading-tight">
            Our Services
          </h2>
          
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Transform your digital presence with our comprehensive web solutions. From e-commerce platforms to AI-powered websites, we craft experiences that drive results.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 xl:grid-cols-4 gap-8 mb-16">
          {services.map((service, index) => (
            <div
              key={index}
              className={`
                group relative bg-white/90 backdrop-blur-sm 
                p-8 rounded-3xl shadow-lg ${service.hoverColor} 
                transform hover:-translate-y-8 hover:rotate-1 
                transition-all duration-700 
                border-2 border-gray-100/50 ${service.borderColor} 
                ${ isVisible ? 'animate-in slide-in-from-bottom-12 fade-in' : 'translate-y-12' }
                group-hover:bg-white/80 group-hover:backdrop-blur-md // MODIFIED: Enhanced blur/transparency on hover
              `}
              style={{ 
                animationDelay: service.delay,
                animationDuration: '900ms',
                animationFillMode: 'forwards'
              }}
              onMouseEnter={() => setHoveredCard(index)}
              onMouseLeave={() => setHoveredCard(null)}
            >
              {/* Popular Badge for AI Website */}
              {index === 2 && (
                <div className="absolute -top-3 -right-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg animate-bounce">
                  🔥 Popular
                </div>
              )}

              {/* Icon with enhanced animations */}
              <div className={`relative w-24 h-24 mx-auto mb-6 rounded-2xl bg-gradient-to-r ${service.color} flex items-center justify-center group-hover:scale-110 group-hover:rotate-12 transition-all duration-500 shadow-xl`}>
                <service.icon className="w-12 h-12 text-white relative z-10" />
                <div className="absolute inset-0 bg-white/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                
                <div className="absolute inset-0 opacity-0  duration-500">
                  {[...Array(6)].map((_, i) => (
                    <div
                      key={i}
                      className="absolute w-1 h-1 bg-white rounded-full animate-ping"
                      style={{
                        top: `${20 + (i * 10)}%`,
                        left: `${15 + (i * 12)}%`,
                        animationDelay: `${i * 200}ms`
                      }}
                    ></div>
                  ))}
                </div>
              </div>

              {/* Content */}
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-gray-800 mb-3 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:bg-clip-text group-hover:from-blue-600 group-hover:to-purple-600 transition-all duration-300">
                  {service.title}
                </h3>
                <p className="text-gray-600 leading-relaxed mb-4">
                  {service.desc}
                </p>

                <div className="space-y-2 mb-6">
                  {service.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center justify-center gap-2 text-sm text-gray-500">
                      <Star className="w-3 h-3 text-yellow-500 fill-current" />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>

                <div className={`inline-block px-4 py-2 rounded-full bg-gradient-to-r ${service.color} text-white font-semibold text-sm mb-4 shadow-lg`}>
                  {service.price}
                </div>
              </div>

              {/* CTA Button */}
              <div className="text-center">
                <button className={`group/btn inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r ${service.color} text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 opacity-90 hover:opacity-100`}>
                  <span>Get Started</span>
                  <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform duration-300" />
                </button>
              </div>

              {/* Enhanced Hover Effects - Color Overlay */}
              <div 
                className={`
                  absolute inset-0 bg-gradient-to-br ${service.bgColor} 
                  opacity-0 group-hover:opacity-50 // MODIFIED: Increased opacity for stronger color tint
                  transition-all duration-700 rounded-3xl -z-10 // Ensure it's behind content
                `}
              ></div>
              
              {/* Animated Border */}
              <div className="absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10"> {/* Ensure it's behind content */}
                <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${service.color} p-[2px] opacity-60`}>
                  {/* MODIFIED: Changed bg-white to bg-transparent to show card's new bg effect */}
                  <div className="w-full h-full bg-transparent rounded-3xl"></div>
                </div>
              </div>
            </div>
          ))}
        </div>

        
      </div>
    </section>
  );
};

export default OurServices;