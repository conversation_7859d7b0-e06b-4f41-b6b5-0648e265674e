import React, { useState, useEffect } from 'react';
import { <PERSON>uote, Star, ChevronLeft, ChevronRight, User, MapPin, Calendar } from 'lucide-react'; // Removed Play, Pause

const TestimonialsSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  // Removed isAutoPlaying state: const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 300);
    return () => clearTimeout(timer);
  }, []);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      role: "E-commerce Entrepreneur",
      company: "Fashion Forward",
      location: "New York, USA",
      date: "March 2024",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "Working with this team transformed our online presence completely. Our e-commerce sales increased by 340% in just 3 months after launch. The attention to detail and user experience is phenomenal!",
      project: "E-commerce Platform",
      highlight: "340% Sales Increase",
      color: "from-pink-500 to-rose-500"
    },
    {
      id: 2,
      name: "Michael Chen",
      role: "Tech Startup Founder",
      company: "InnovateLab",
      location: "San Francisco, USA",
      date: "February 2024",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "The AI integration they built for our platform is incredible. Our users love the smart features and the website performance is lightning fast. Best investment we've made for our startup!",
      project: "AI-Powered Website",
      highlight: "Lightning Fast Performance",
      color: "from-blue-500 to-indigo-500"
    },
    {
      id: 3,
      name: "Emily Rodriguez",
      role: "Creative Director",
      company: "Art & Design Studio",
      location: "Barcelona, Spain",
      date: "January 2024",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "My portfolio website exceeded all expectations. The design is stunning and perfectly captures my artistic vision. I've received 5x more client inquiries since the launch!",
      project: "Portfolio Website",
      highlight: "5x More Inquiries",
      color: "from-purple-500 to-violet-500"
    },
    {
      id: 4,
      name: "David Thompson",
      role: "Restaurant Owner",
      company: "Gourmet Bistro",
      location: "London, UK",
      date: "April 2024",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "Our restaurant's online ordering system is seamless and beautiful. Customer satisfaction has improved dramatically, and our online orders have doubled. Exceptional work!",
      project: "Restaurant Website",
      highlight: "Doubled Online Orders",
      color: "from-green-500 to-emerald-500"
    },
    {
      id: 5,
      name: "Lisa Wang",
      role: "Fitness Coach",
      company: "FitLife Training",
      location: "Toronto, Canada",
      date: "May 2024",
      avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "The custom booking system and client portal they created has revolutionized my business. My clients love the user-friendly interface and I've streamlined all my operations!",
      project: "Business Platform",
      highlight: "Streamlined Operations",
      color: "from-orange-500 to-amber-500"
    }
  ];

  // Auto-play functionality (now always on)
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [testimonials.length]); // Removed isAutoPlaying dependency

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % testimonials.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const currentTestimonial = testimonials[currentSlide];

  return (
    <section className="py-24 px-4 bg-gradient-to-br from-gray-50 to-blue-50 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/2 left-1/3 w-64 h-64 bg-pink-500/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '4s' }}></div>
        
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white/20 rounded-full animate-twinkle"
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${2 + Math.random() * 3}s`
            }}
          ></div>
        ))}
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        {/* Header */}
        <div className="text-center mb-16">
          
          <h2 className={`text-6xl md:text-7xl font-bold mb-6  bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 bg-clip-text text-transparent leading-tight ${
            isVisible ? 'animate-in slide-in-from-bottom-8 fade-in' : 'opacity-0'
          }`}>
            Our Client Opinoin
          </h2>
          
          <p className={`text-xl text-black max-w-3xl mx-auto leading-relaxed ${
            isVisible ? 'animate-in slide-in-from-bottom-8 fade-in' : 'opacity-0'
          }`} style={{ animationDelay: '200ms' }}>
            Real results from real clients. See how we've helped businesses transform their digital presence.
          </p>
        </div>

        {/* Main Testimonial Card */}
        <div className="relative max-w-6xl mx-auto mb-12">
          <div className={`bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl p-8 md:p-12 border border-white/20 transform transition-all duration-700 ${
            isVisible ? 'animate-in slide-in-from-bottom-12 fade-in' : 'opacity-0'
          }`} style={{ animationDelay: '400ms' }}>
            
            <div className={`w-20 h-20 mx-auto mb-8 rounded-full bg-gradient-to-r ${currentTestimonial.color} flex items-center justify-center shadow-lg transform rotate-12`}>
              <Quote className="w-10 h-10 text-white" />
            </div>

            <div className="flex justify-center mb-6">
              {[...Array(currentTestimonial.rating)].map((_, i) => (
                <Star 
                  key={i} 
                  className="w-6 h-6 text-yellow-400 fill-current mx-1 animate-bounce" 
                  style={{ animationDelay: `${i * 100}ms` }}
                />
              ))}
            </div>

            <blockquote className="text-2xl md:text-3xl text-gray-700 font-medium text-center leading-relaxed mb-8 italic">
              "{currentTestimonial.text}"
            </blockquote>

           
            <div className="flex flex-col md:flex-row items-center justify-center gap-6">
              <div className="flex items-center gap-4">
                <div className="relative">
                  <img 
                    src={currentTestimonial.avatar} 
                    alt={currentTestimonial.name}
                    className="w-16 h-16 rounded-full object-cover shadow-lg border-4 border-white"
                  />
                  <div className={`absolute -bottom-1 -right-1 w-6 h-6 bg-gradient-to-r ${currentTestimonial.color} rounded-full border-2 border-white flex items-center justify-center`}>
                    <User className="w-3 h-3 text-white" />
                  </div>
                </div>
                
                <div className="text-center md:text-left">
                  <h4 className="text-xl font-bold text-gray-800">{currentTestimonial.name}</h4>
                  <p className="text-gray-600 font-medium">{currentTestimonial.role}</p>
                  <p className="text-gray-500">{currentTestimonial.company}</p>
                </div>
              </div>

              <div className="flex flex-col md:flex-row gap-4 text-sm text-gray-500">
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4" />
                  {currentTestimonial.location}
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  {currentTestimonial.date}
                </div>
              </div>
            </div>

            <div className="text-center mt-6">
              <span className="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium">
                Project: {currentTestimonial.project}
              </span>
            </div>
          </div>

          <button 
            onClick={prevSlide}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full shadow-lg border border-white/20 flex items-center justify-center hover:bg-white hover:scale-110 transition-all duration-300 text-gray-700"
          >
            <ChevronLeft className="w-6 h-6" />
          </button>
          
          <button 
            onClick={nextSlide}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full shadow-lg border border-white/20 flex items-center justify-center hover:bg-white hover:scale-110 transition-all duration-300 text-gray-700"
          >
            <ChevronRight className="w-6 h-6" />
          </button>
        </div>

        {/* Controls (Only Slide Indicators now) */}
        <div className="flex items-center justify-center gap-6 mb-12">
          {/* Slide Indicators */}
          <div className="flex gap-3">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentSlide 
                    ? `bg-gradient-to-r ${currentTestimonial.color} scale-125` 
                    : 'bg-gray-500 hover:bg-white/50'
                }`}
              />
            ))}
          </div>

        </div>


      </div>

      <style jsx>{`
        @keyframes twinkle {
          0%, 100% { opacity: 0.2; }
          50% { opacity: 1; }
        }
        .animate-twinkle {
          animation: twinkle 3s ease-in-out infinite;
        }
      `}</style>
    </section>
  );
};

export default TestimonialsSection;