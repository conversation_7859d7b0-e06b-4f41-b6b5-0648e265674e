import React, { useState } from 'react';
import { <PERSON>R<PERSON>, Phone, Eye, Star } from 'lucide-react';
import { Link } from 'react-router-dom';

const ServiceCard = ({ 
  icon: Icon, 
  title, 
  description, 
  features, 
  color, 
  bgColor, 
  hoverColor, 
  borderColor, 
  price, 
  delay = "0ms",
  contactLink = "/contact",
  viewServicesLink = "#",
  isVisible = true 
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className={`
        group relative bg-white/90 backdrop-blur-sm 
        p-8 rounded-3xl shadow-lg ${hoverColor} 
        transform hover:-translate-y-8 hover:rotate-1 
        transition-all duration-700 
        border-2 border-gray-100/50 ${borderColor} 
        ${isVisible ? 'animate-in slide-in-from-bottom-12 fade-in' : 'translate-y-12'}
        hover:bg-white/95 hover:backdrop-blur-md
      `}
      style={{ 
        animationDelay: delay,
        animationDuration: '900ms',
        animationFillMode: 'forwards'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Icon with enhanced animations */}
      <div className={`relative w-24 h-24 mx-auto mb-6 rounded-2xl bg-gradient-to-r ${color} flex items-center justify-center group-hover:scale-110 group-hover:rotate-12 transition-all duration-500 shadow-xl`}>
        <Icon className="w-12 h-12 text-white relative z-10" />
        <div className="absolute inset-0 bg-white/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        
        {/* Animated particles */}
        <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-white rounded-full animate-ping"
              style={{
                top: `${20 + (i * 10)}%`,
                left: `${15 + (i * 12)}%`,
                animationDelay: `${i * 200}ms`
              }}
            ></div>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold text-gray-800 mb-3 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:bg-clip-text group-hover:from-blue-600 group-hover:to-purple-600 transition-all duration-300">
          {title}
        </h3>
        <p className="text-gray-600 leading-relaxed mb-4">
          {description}
        </p>

        {/* Features */}
        <div className="space-y-2 mb-6">
          {features.map((feature, featureIndex) => (
            <div key={featureIndex} className="flex items-center justify-center gap-2 text-sm text-gray-500">
              <Star className="w-3 h-3 text-yellow-500 fill-current" />
              <span>{feature}</span>
            </div>
          ))}
        </div>

        {/* Price Badge */}
        <div className={`inline-block px-4 py-2 rounded-full bg-gradient-to-r ${color} text-white font-semibold text-sm mb-6 shadow-lg`}>
          {price}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="space-y-3">
        {/* Primary CTA - Contact Us */}
        <Link 
          to={contactLink}
          className={`group/btn w-full inline-flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r ${color} text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300`}
        >
          <Phone className="w-4 h-4" />
          <span>Contact Us</span>
          <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform duration-300" />
        </Link>

        {/* Secondary CTA - View Services */}
        <Link 
          to={viewServicesLink}
          className="group/btn w-full inline-flex items-center justify-center gap-2 px-6 py-3 bg-white border-2 border-gray-200 text-gray-700 rounded-xl font-semibold hover:border-gray-300 hover:bg-gray-50 transform hover:scale-105 transition-all duration-300"
        >
          <Eye className="w-4 h-4" />
          <span>View Services</span>
          <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform duration-300" />
        </Link>
      </div>

      {/* Enhanced Hover Effects - Color Overlay */}
      <div 
        className={`
          absolute inset-0 bg-gradient-to-br ${bgColor} 
          opacity-0 group-hover:opacity-50 
          transition-all duration-700 rounded-3xl -z-10
        `}
      ></div>
      
      {/* Animated Border */}
      <div className="absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10">
        <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${color} p-[2px] opacity-60`}>
          <div className="w-full h-full bg-transparent rounded-3xl"></div>
        </div>
      </div>
    </div>
  );
};

export default ServiceCard;
