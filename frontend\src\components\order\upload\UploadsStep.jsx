import React from 'react';
import { FileUploadField } from './FileUploadField'; // Import from the same 'upload' folder
import { Upload, Image, Globe, Palette, FileText, CheckCircle } from 'lucide-react'; // Icons used in this step

const UploadsStep = ({ formData, handleFileUpload, errors }) => {
  // Ensure formData and formData.uploads exist to prevent errors
  const uploads = formData?.uploads || {};
  const currentErrors = errors || {}; // Ensure errors object exists

  const uploadFields = [
    {
      label: "Upload Your Logo",
      name: "logo",
      fileName: uploads.logo?.name,
      helpText: "SVG, PNG, AI, EPS • Max 10MB",
      error: currentErrors.logoUpload || currentErrors.logo, // Check for specific or general error key
      icon: Image,
      priority: 'high'
    },
    {
      label: "Sample Websites or Inspirations",
      name: "sampleSites",
      fileName: uploads.sampleSites?.name,
      helpText: "Images, screenshots, or docs with links",
      error: currentErrors.sampleSitesUpload || currentErrors.sampleSites,
      icon: Globe,
      priority: 'medium'
    },
    {
      label: "Brand Colors or Guidelines",
      name: "brandColors",
      fileName: uploads.brandColors?.name,
      helpText: "PDF, image, or a doc with color codes",
      error: currentErrors.brandColorsUpload || currentErrors.brandColors,
      icon: Palette,
      priority: 'medium'
    },
    {
      label: "Other Documents (Notes, Briefs, etc.)",
      name: "documents",
      fileName: uploads.documents?.name,
      helpText: "PDFs, Word documents, text files",
      error: currentErrors.documentsUpload || currentErrors.documents,
      icon: FileText,
      priority: 'low'
    },
  ];

  const uploadedCount = uploadFields.filter(field => field.fileName).length;
  const totalCount = uploadFields.length;
  const progressPercentage = totalCount > 0 ? (uploadedCount / totalCount) * 100 : 0;

  // The 'onChange' prop for FileUploadField should be handleFileUpload from the parent (OrderPage)
  // which expects (fieldName, fileObject)
  const onFileChange = (fieldName, file) => {
    handleFileUpload(fieldName, file);
  };


  return (
    <div className="max-w-5xl mx-auto">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl mb-4 shadow-lg">
          <Upload className="w-10 h-10 text-white" />
        </div>
        <h2 className="text-3xl font-bold text-slate-800 mb-2">
          📤 Share Your Vision
        </h2>
        <p className="text-slate-600 max-w-2xl mx-auto leading-relaxed">
          Upload your branding materials, inspirations, and documents to help us understand your vision better.
          This step is optional but highly recommended for the best results.
        </p>
      </div>

      <div className="bg-white rounded-2xl border border-slate-200 p-6 mb-8 shadow-sm">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-slate-800">Upload Progress</h3>
            <p className="text-sm text-slate-600">
              {uploadedCount === 0 && "Ready to upload your files"}
              {uploadedCount > 0 && uploadedCount < totalCount && `Great progress! ${uploadedCount} of ${totalCount} uploaded.`}
              {uploadedCount === totalCount && totalCount > 0 && "All recommended files uploaded! 🎉"}
              {totalCount === 0 && "No files to upload."}
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-slate-800">{uploadedCount}/{totalCount}</div>
            <div className="text-sm text-slate-600">files uploaded</div>
          </div>
        </div>
        <div className="w-full bg-slate-200 rounded-full h-3 overflow-hidden">
          <div
            className="h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-700 ease-out shadow-sm"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {uploadFields.map((field) => (
          <FileUploadField
            key={field.name}
            label={field.label}
            name={field.name}
            fileName={field.fileName}
            onChange={onFileChange} // Use the correctly scoped onFileChange
            helpText={field.helpText}
            error={field.error}
            icon={field.icon}
            priority={field.priority}
          />
        ))}
      </div>

      <div className="bg-gradient-to-r from-slate-50 to-blue-50 rounded-2xl border border-slate-200 p-8">
        <div className="text-center mb-6">
          <h3 className="text-xl font-semibold text-slate-800 mb-2">
            💡 Upload Tips & Guidelines
          </h3>
          <p className="text-slate-600">
            Follow these best practices for optimal results
          </p>
        </div>
        <div className="grid md:grid-cols-3 gap-6">
          <div className="text-center p-4 bg-white rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Image className="w-6 h-6 text-blue-600" />
            </div>
            <h4 className="font-semibold text-slate-800 mb-1">High Quality</h4>
            <p className="text-xs text-slate-600">
              Use high-res images & vector formats (SVG, AI) for logos.
            </p>
          </div>
          <div className="text-center p-4 bg-white rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-3">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <h4 className="font-semibold text-slate-800 mb-1">Clear Naming</h4>
            <p className="text-xs text-slate-600">
              Name files clearly (e.g., 'CompanyName_Logo.png').
            </p>
          </div>
          <div className="text-center p-4 bg-white rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-3">
              <FileText className="w-6 h-6 text-purple-600" />
            </div>
            <h4 className="font-semibold text-slate-800 mb-1">Relevant Docs</h4>
            <p className="text-xs text-slate-600">
              Only upload documents directly related to the project brief.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UploadsStep; // Default export for the step component