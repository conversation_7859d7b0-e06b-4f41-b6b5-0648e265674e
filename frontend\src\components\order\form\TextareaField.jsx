import React, { useState } from 'react';
import { AlertCircle, Info } from 'lucide-react'; // Import necessary icons

export const TextareaField = ({
  label,
  name,
  value,
  onChange,
  placeholder,
  error,
  optional,
  disabled,
  rows = 4,
  helpText,
  icon: IconComponent // Renamed to avoid conflict
}) => {
  const [isFocused, setIsFocused] = useState(false);

  return (
    <div className="group w-full"> {/* Ensure it takes full width if in a grid */}
      <div className="flex items-center justify-between mb-1.5">
        <label htmlFor={name} className="flex items-center text-sm font-semibold text-slate-700">
          {IconComponent && <IconComponent size={16} className="mr-2 text-slate-500 flex-shrink-0" />}
          {label}
          {optional && (
            <span className="ml-2 text-xs font-normal text-slate-500 bg-slate-100 px-1.5 py-0.5 rounded-full">
              Optional
            </span>
          )}
        </label>
        {helpText && (
          <div className="relative group/tooltip">
            <Info size={16} className="text-slate-400 cursor-help hover:text-slate-600" />
            <div className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 w-max max-w-xs p-2.5 bg-slate-800 text-white text-xs rounded-lg shadow-lg opacity-0 pointer-events-none group-hover/tooltip:opacity-100 transition-opacity duration-200 z-20">
              {helpText}
            </div>
          </div>
        )}
      </div>
      <textarea
        name={name}
        id={name}
        value={value || ''} // Ensure value is not undefined
        onChange={onChange}
        placeholder={placeholder}
        rows={rows}
        disabled={disabled}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        className={`
          w-full px-4 py-3 border-2 rounded-xl shadow-sm transition-all duration-300
          text-slate-800 placeholder-slate-400 text-sm
          focus:outline-none
          ${error
            ? 'border-red-400 bg-red-50 focus:border-red-500 focus:ring-4 focus:ring-red-600/20'
            : isFocused
              ? 'border-blue-500 bg-blue-50 focus:ring-4 focus:ring-blue-600/20'
              : 'border-slate-300 bg-white hover:border-slate-400 focus:border-blue-500'
          }
          ${disabled
            ? 'bg-slate-100 text-slate-500 cursor-not-allowed hover:border-slate-300'
            : ''
          }
        `}
      />
      {error && (
        <div className="mt-1.5 flex items-center text-red-600 text-xs">
          <AlertCircle size={14} className="mr-1 flex-shrink-0" />
          {error}
        </div>
      )}
    </div>
  );
};