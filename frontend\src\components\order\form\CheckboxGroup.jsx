import React from 'react';
import { Check, AlertCircle, Info } from 'lucide-react';

export const CheckboxGroup = ({ label, name, options, selectedOptions, onChange, error, disabled, helpText }) => (
  <div className="space-y-3">
    <div className="flex items-center justify-between">
      <label className="text-sm font-semibold text-slate-700">{label}</label>
      {helpText && (
        <div className="relative group/tooltip">
          <Info className="w-4 h-4 text-slate-400 cursor-help" />
          <div className="absolute right-0 bottom-full mb-2 w-64 p-2 bg-slate-800 text-white text-xs rounded-lg opacity-0 pointer-events-none group-hover/tooltip:opacity-100 transition-opacity duration-200 z-10">
            {helpText}
          </div>
        </div>
      )}
    </div>
    
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
      {options.map((option) => {
        const isSelected = selectedOptions.includes(option.value);
        return (
          <label 
            key={option.value} 
            className={`
              relative flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-300 group
              ${disabled 
                ? 'bg-slate-50 border-slate-200 cursor-not-allowed opacity-60' 
                : isSelected
                  ? 'bg-green-50 border-green-500 shadow-lg shadow-green-100'
                  : 'bg-white border-slate-200 hover:border-slate-300 hover:shadow-md'
              }
            `}
          >
            <input
              type="checkbox"
              name={name} // Should be option.value for individual checkbox 'name' if handled separately, or common name if group
              value={option.value}
              checked={isSelected}
              onChange={onChange}
              disabled={disabled}
              className="sr-only"
            />
            
            <div className={`
              w-5 h-5 rounded-md border-2 mr-3 flex items-center justify-center transition-all duration-300
              ${isSelected 
                ? 'border-green-500 bg-green-500' 
                : 'border-slate-300 bg-white group-hover:border-slate-400'
              }
            `}>
              {isSelected && <Check className="w-3 h-3 text-white" />}
            </div>
            
            <div className="flex-1">
              <span className={`text-sm font-medium ${disabled ? 'text-slate-500' : 'text-slate-700'}`}>
                {option.label}
              </span>
              {option.description && (
                <p className="text-xs text-slate-500 mt-1">{option.description}</p>
              )}
            </div>
          </label>
        );
      })}
    </div>
    
    {error && (
      <div className="mt-2 flex items-center text-red-600 text-sm animate-in slide-in-from-top-1 duration-200">
        <AlertCircle className="w-4 h-4 mr-1" />
        {error}
      </div>
    )}
  </div>
);