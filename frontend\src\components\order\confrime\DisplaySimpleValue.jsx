import React from 'react';
// No specific icons needed by default, they are passed as IconComponent prop

export const DisplaySimpleValue = ({ label, value, placeholder = "Not provided", icon: IconComponent, isCurrency = false }) => (
  <div className="py-3 sm:grid sm:grid-cols-3 sm:gap-4 items-start">
    <dt className="text-sm font-medium text-slate-500 flex items-center">
      {IconComponent && <IconComponent size={16} className="mr-2 text-slate-400 flex-shrink-0" />}
      {label}:
    </dt>
    <dd className="mt-1 text-sm text-slate-900 sm:mt-0 sm:col-span-2 break-words">
      {value ? (
        isCurrency ? <span className="font-semibold text-indigo-600">{value}</span> : value
      ) : (
        <span className="italic text-slate-500">{placeholder}</span>
      )}
    </dd>
  </div>
);

export default DisplaySimpleValue; // Also adding a default export