import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, ExternalLink, Clock, Users, Star, ArrowRight } from 'lucide-react';

export default function ProjectSlider() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Sample project data - replace with your actual projects
  const projects = [
    {
      id: 1,
      title: "E-Commerce Fashion Store",
      category: "E-Commerce",
      image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      deliveryTime: "18 hours",
      clientRating: 5,
      description: "Modern fashion e-commerce platform with advanced filtering, wishlist, and seamless checkout experience.",
      tags: ["React", "Stripe", "Responsive", "SEO"],
      clientName: "StyleHub"
    },
    {
      id: 2,
      title: "Restaurant Management System",
      category: "Business",
      image: "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      deliveryTime: "22 hours",
      clientRating: 5,
      description: "Complete restaurant website with online ordering, table reservations, and menu management system.",
      tags: ["Vue.js", "Node.js", "Real-time", "Mobile"],
      clientName: "Bella Vista"
    },
    {
      id: 3,
      title: "Digital Marketing Agency",
      category: "Agency",
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2015&q=80",
      deliveryTime: "20 hours",
      clientRating: 5,
      description: "Professional agency website with portfolio showcase, client testimonials, and lead generation forms.",
      tags: ["Next.js", "Animations", "CMS", "Analytics"],
      clientName: "Growth Labs"
    },
    {
      id: 4,
      title: "Healthcare Clinic Portal",
      category: "Healthcare",
      image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80",
      deliveryTime: "24 hours",
      clientRating: 5,
      description: "Secure healthcare platform with appointment booking, patient portal, and telemedicine integration.",
      tags: ["React", "Security", "HIPAA", "Dashboard"],
      clientName: "MediCare Plus"
    },
    {
      id: 5,
      title: "Real Estate Platform",
      category: "Real Estate",
      image: "https://images.unsplash.com/photo-**********-ce09059eeffa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1973&q=80",
      deliveryTime: "19 hours",
      clientRating: 5,
      description: "Advanced property listing platform with virtual tours, mortgage calculators, and agent profiles.",
      tags: ["Angular", "Maps", "Search", "Mobile"],
      clientName: "PropertyPro"
    }
  ];

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return;
    
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % projects.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, projects.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % projects.length);
    setIsAutoPlaying(false);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + projects.length) % projects.length);
    setIsAutoPlaying(false);
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
    setIsAutoPlaying(false);
  };

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50 relative overflow-hidden">
      {/* Background Decorations */}
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="absolute top-20 right-10 w-64 h-64 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute bottom-20 left-10 w-80 h-80 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-pulse delay-1000"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            Our Latest <span className="text-blue-600">Projects</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover how we've transformed businesses with stunning websites delivered in record time
          </p>
        </div>

        {/* Main Slider Container */}
        <div className="relative max-w-6xl mx-auto">
          
          {/* Slider Wrapper */}
          <div className="relative bg-white rounded-3xl shadow-2xl overflow-hidden">
            
            {/* Current Project Display */}
            <div className="flex flex-col lg:flex-row min-h-[600px]">
              
              {/* Project Image */}
              <div className="lg:w-3/5 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-transparent z-10"></div>
                <img 
                  src={projects[currentSlide].image}
                  alt={projects[currentSlide].title}
                  className="w-full h-full object-cover transform scale-105 hover:scale-110 transition-transform duration-700"
                />
                
                {/* Category Badge */}
                <div className="absolute top-6 left-6 z-20">
                  <span className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
                    {projects[currentSlide].category}
                  </span>
                </div>

                {/* Quick Stats */}
                <div className="absolute bottom-6 left-6 right-6 z-20 flex space-x-4">
                  <div className="bg-white/90 backdrop-blur-sm rounded-xl px-4 py-2 flex items-center">
                    <Clock className="w-4 h-4 text-blue-600 mr-2" />
                    <span className="text-sm font-semibold text-gray-800">{projects[currentSlide].deliveryTime}</span>
                  </div>
                  <div className="bg-white/90 backdrop-blur-sm rounded-xl px-4 py-2 flex items-center">
                    <div className="flex items-center">
                      {[...Array(projects[currentSlide].clientRating)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Project Info */}
              <div className="lg:w-2/5 p-8 lg:p-12 flex flex-col justify-center">
                <div className="mb-6">
                  <h3 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4 leading-tight">
                    {projects[currentSlide].title}
                  </h3>
                  <p className="text-gray-600 text-lg leading-relaxed mb-6">
                    {projects[currentSlide].description}
                  </p>
                </div>

                {/* Tags */}
                <div className="mb-8">
                  <div className="flex flex-wrap gap-2">
                    {projects[currentSlide].tags.map((tag, index) => (
                      <span 
                        key={index}
                        className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm font-medium"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Client Info */}
                <div className="mb-8">
                  <div className="flex items-center text-gray-600">
                    <Users className="w-5 h-5 mr-2" />
                    <span className="font-medium">Client: {projects[currentSlide].clientName}</span>
                  </div>
                </div>

                {/* CTA Button */}
                <button className="group bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-2xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl flex items-center justify-center">
                  View Project Details
                  <ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </button>
              </div>
            </div>
          </div>

          {/* Navigation Arrows */}
          <button 
            onClick={prevSlide}
            className="absolute left-4 top-1/2 -translate-y-1/2 bg-white hover:bg-gray-50 text-gray-800 p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 z-20"
          >
            <ChevronLeft className="w-6 h-6" />
          </button>
          
          <button 
            onClick={nextSlide}
            className="absolute right-4 top-1/2 -translate-y-1/2 bg-white hover:bg-gray-50 text-gray-800 p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 z-20"
          >
            <ChevronRight className="w-6 h-6" />
          </button>

          {/* Slide Indicators */}
          <div className="flex justify-center mt-8 space-x-3">
            {projects.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentSlide 
                    ? 'bg-blue-600 w-8' 
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>

          {/* Progress Bar */}
          <div className="mt-6 max-w-md mx-auto">
            <div className="bg-gray-200 rounded-full h-1">
              <div 
                className="bg-blue-600 h-1 rounded-full transition-all duration-300"
                style={{ width: `${((currentSlide + 1) / projects.length) * 100}%` }}
              />
            </div>
            <div className="flex justify-between text-sm text-gray-500 mt-2">
              <span>Project {currentSlide + 1}</span>
              <span>{projects.length} Total</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}