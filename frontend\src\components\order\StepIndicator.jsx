import React from 'react';
// Check icon is not used in this new design based on the image
// import { Check } from 'lucide-react';

const StepIndicator = ({ currentStep, totalSteps, titles }) => {
  const steps = Array.from({ length: totalSteps }, (_, i) => i + 1);

  return (
    // Added 'hidden md:block' for responsive visibility
    <div className="hidden md:block mb-12 select-none pt-4"> {/* Increased mb and added pt for space */}
      <div className="flex items-center">
        {steps.map((step, index) => {
          const isActive = currentStep === step;
          // A step is considered "passed" if the current step is beyond it.
          // The line leading *to* an active step should also be blue.
          const isLineActiveBefore = currentStep >= step;
          const isLineActiveAfter = currentStep > step; // For the line segment after a circle

          return (
            <React.Fragment key={step}>
              {/* Line segment BEFORE the circle (except for the very first conceptual "start" which isn't a typical line) */}
              {/* This structure assumes a line is visually between each step definition */}
              {index > 0 && (
                <div className={`flex-1 h-[3px] mx-1 transition-all duration-500 ease-in-out rounded-full
                  group-hover:bg-slate-400  /* Example hover effect on parent if steps were clickable */
                  sm:mx-2
                `}></div>
              )}

              {/* Step Circle and Label */}
              <div className="flex flex-col items-center relative group cursor-default"> {/* Removed cursor-pointer unless steps are clickable */}
                <div
                  className={`
                    w-10 h-10 md:w-11 md:h-11 rounded-full flex items-center justify-center border-2
                    transition-all duration-500 ease-in-out transform relative
                    ${isActive
                      ? 'bg-blue-600 border-blue-600 text-white scale-110 shadow-lg ring-4 ring-blue-300 ring-opacity-60'
                      : 'border-slate-300 text-slate-500'}
                    ${!isActive && currentStep > step // Completed step look (empty circle but blue border, leading line is blue)
                      ? 'bg-white border-blue-600'
                      : ''}
                    ${!isActive && currentStep < step // Future step look
                      ? 'bg-slate-50 border-slate-300 text-slate-400'
                      : ''}
                  `}
                >
                  <span className={`text-base md:text-lg font-semibold transition-colors duration-300
                    ${isActive ? 'text-white' : ''}
                    ${!isActive && currentStep > step ? 'text-blue-600' : ''}
                    ${!isActive && currentStep < step ? 'text-slate-400' : ''}
                  `}>
                    {step}
                  </span>
                </div>
                <p className={`
                  mt-3 text-xs md:text-sm text-center transition-all duration-500 ease-in-out
                  font-medium w-24 md:w-28
                  ${isActive ? 'text-blue-600 font-semibold' : 'text-slate-500'}
                  ${!isActive && currentStep > step ? 'text-slate-700' : ''}
                  ${!isActive && currentStep < step ? 'text-slate-400' : ''}
                `}>
                  {titles[index]}
                </p>
              </div>
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};

export default StepIndicator;