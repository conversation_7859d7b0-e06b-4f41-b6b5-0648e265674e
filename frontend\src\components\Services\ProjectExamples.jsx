import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Filter } from 'lucide-react';
import ProjectCard from './ProjectCard';

const ProjectExamples = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [activeFilter, setActiveFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(0);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 300);
    return () => clearTimeout(timer);
  }, []);

  const projectCategories = [
    { id: 'all', name: 'All Projects', count: 24 },
    { id: 'fullstack', name: 'Full-Stack', count: 8 },
    { id: 'mobile', name: 'Mobile Apps', count: 8 },
    { id: 'frontend', name: 'Frontend', count: 8 }
  ];

  const projects = [
    // Full-Stack Projects
    {
      id: 1,
      title: "E-Commerce Fashion Platform",
      description: "Complete online fashion store with advanced filtering, wishlist, secure payments, inventory management, and admin dashboard.",
      image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["React", "Node.js", "MongoDB", "Stripe", "AWS"],
      category: "fullstack",
      deliveryTime: "3 weeks",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 2,
      title: "Educational Learning Management System",
      description: "Comprehensive LMS with course management, student tracking, video streaming, assignments, and progress analytics.",
      image: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["Vue.js", "Express", "PostgreSQL", "Socket.io", "Docker"],
      category: "fullstack",
      deliveryTime: "4 weeks",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 3,
      title: "AI-Powered Analytics Dashboard",
      description: "Intelligent business analytics platform with machine learning insights, predictive modeling, and real-time data visualization.",
      image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["React", "Python", "TensorFlow", "FastAPI", "Redis"],
      category: "fullstack",
      deliveryTime: "5 weeks",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 4,
      title: "Hotel Booking & Management System",
      description: "Complete hotel management solution with booking engine, room management, payment processing, and guest services.",
      image: "https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["Angular", "Spring Boot", "MySQL", "PayPal", "Azure"],
      category: "fullstack",
      deliveryTime: "4 weeks",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 5,
      title: "Healthcare Management Platform",
      description: "Comprehensive healthcare system with patient records, appointment scheduling, telemedicine, and billing integration.",
      image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["React", "Django", "PostgreSQL", "WebRTC", "HIPAA"],
      category: "fullstack",
      deliveryTime: "6 weeks",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 6,
      title: "Real Estate Marketplace",
      description: "Property listing platform with advanced search, virtual tours, mortgage calculator, and agent management system.",
      image: "https://images.unsplash.com/photo-**********-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["Next.js", "Node.js", "MongoDB", "Mapbox", "Cloudinary"],
      category: "fullstack",
      deliveryTime: "4 weeks",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 7,
      title: "Social Media Platform",
      description: "Modern social networking platform with real-time messaging, content sharing, live streaming, and community features.",
      image: "https://images.unsplash.com/photo-*************-80b023f02d71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["React", "GraphQL", "MongoDB", "Socket.io", "AWS S3"],
      category: "fullstack",
      deliveryTime: "5 weeks",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 8,
      title: "Financial Banking Application",
      description: "Secure banking platform with account management, transactions, loan processing, and advanced security features.",
      image: "https://images.unsplash.com/photo-**********-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["React", "Java", "Oracle", "Spring Security", "Blockchain"],
      category: "fullstack",
      deliveryTime: "8 weeks",
      rating: 5,
      projectLink: "#"
    },

    // Mobile App Projects
    {
      id: 9,
      title: "Fitness Tracking Mobile App",
      description: "Comprehensive fitness app with workout tracking, nutrition planning, progress analytics, and social features.",
      image: "https://images.unsplash.com/photo-*************-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["React Native", "Firebase", "HealthKit", "Google Fit"],
      category: "mobile",
      deliveryTime: "3 weeks",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 10,
      title: "Food Delivery Mobile App",
      description: "Complete food delivery solution with restaurant listings, real-time tracking, payment integration, and driver app.",
      image: "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["Flutter", "Node.js", "MongoDB", "Google Maps", "Stripe"],
      category: "mobile",
      deliveryTime: "4 weeks",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 11,
      title: "E-Learning Mobile Platform",
      description: "Educational mobile app with offline content, interactive quizzes, progress tracking, and certification system.",
      image: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["React Native", "Redux", "SQLite", "Video.js", "AWS"],
      category: "mobile",
      deliveryTime: "3 weeks",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 12,
      title: "Travel Companion App",
      description: "Smart travel app with itinerary planning, local recommendations, expense tracking, and offline maps.",
      image: "https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["Flutter", "Dart", "Google Maps", "TripAdvisor API"],
      category: "mobile",
      deliveryTime: "3 weeks",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 13,
      title: "Meditation & Wellness App",
      description: "Mindfulness app with guided meditations, sleep stories, mood tracking, and personalized wellness plans.",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["React Native", "Redux", "Audio APIs", "HealthKit"],
      category: "mobile",
      deliveryTime: "2 weeks",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 14,
      title: "Cryptocurrency Trading App",
      description: "Secure crypto trading platform with real-time charts, portfolio tracking, news feed, and advanced trading tools.",
      image: "https://images.unsplash.com/photo-1621761191319-c6fb62004040?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["Flutter", "WebSocket", "Chart.js", "Biometric Auth"],
      category: "mobile",
      deliveryTime: "4 weeks",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 15,
      title: "Smart Home Control App",
      description: "IoT home automation app with device control, energy monitoring, security features, and voice commands.",
      image: "https://images.unsplash.com/photo-**********-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["React Native", "IoT", "Bluetooth", "Voice Recognition"],
      category: "mobile",
      deliveryTime: "3 weeks",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 16,
      title: "Language Learning App",
      description: "Interactive language learning platform with speech recognition, gamification, progress tracking, and offline lessons.",
      image: "https://images.unsplash.com/photo-1434030216411-0b793f4b4173?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["Flutter", "Speech API", "SQLite", "Gamification"],
      category: "mobile",
      deliveryTime: "4 weeks",
      rating: 5,
      projectLink: "#"
    },

    // Frontend Projects
    {
      id: 17,
      title: "Modern Portfolio Website",
      description: "Stunning personal portfolio with interactive animations, project showcases, blog integration, and contact forms.",
      image: "https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["React", "Framer Motion", "Tailwind CSS", "Netlify"],
      category: "frontend",
      deliveryTime: "1 week",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 18,
      title: "Corporate Business Website",
      description: "Professional corporate website with service pages, team profiles, testimonials, and lead generation forms.",
      image: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["Vue.js", "Nuxt.js", "SCSS", "Contentful CMS"],
      category: "frontend",
      deliveryTime: "2 weeks",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 19,
      title: "Restaurant Menu & Ordering",
      description: "Interactive restaurant website with digital menu, online ordering, table reservations, and customer reviews.",
      image: "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["React", "Styled Components", "Stripe", "Google Maps"],
      category: "frontend",
      deliveryTime: "2 weeks",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 20,
      title: "Creative Agency Showcase",
      description: "Artistic agency website with portfolio galleries, case studies, team showcase, and interactive contact forms.",
      image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["Next.js", "Three.js", "GSAP", "Sanity CMS"],
      category: "frontend",
      deliveryTime: "3 weeks",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 21,
      title: "Event Management Platform",
      description: "Event website with ticket booking, event calendar, speaker profiles, and live streaming integration.",
      image: "https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["Angular", "TypeScript", "Bootstrap", "PayPal"],
      category: "frontend",
      deliveryTime: "2 weeks",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 22,
      title: "News & Blog Platform",
      description: "Modern news website with article management, category filtering, search functionality, and social sharing.",
      image: "https://images.unsplash.com/photo-1504711434969-e33886168f5c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["React", "Gatsby", "GraphQL", "Markdown"],
      category: "frontend",
      deliveryTime: "2 weeks",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 23,
      title: "Fitness Studio Website",
      description: "Dynamic fitness website with class schedules, trainer profiles, membership plans, and online booking system.",
      image: "https://images.unsplash.com/photo-*************-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["Vue.js", "Vuetify", "Calendar API", "Stripe"],
      category: "frontend",
      deliveryTime: "2 weeks",
      rating: 5,
      projectLink: "#"
    },
    {
      id: 24,
      title: "SaaS Landing Page",
      description: "High-converting SaaS landing page with feature highlights, pricing tables, testimonials, and signup forms.",
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      technologies: ["React", "Tailwind CSS", "Framer Motion", "Vercel"],
      category: "frontend",
      deliveryTime: "1 week",
      rating: 5,
      projectLink: "#"
    }
  ];

  const filteredProjects = activeFilter === 'all' 
    ? projects 
    : projects.filter(project => project.category === activeFilter);

  const projectsPerPage = 6;
  const totalPages = Math.ceil(filteredProjects.length / projectsPerPage);
  const currentProjects = filteredProjects.slice(
    currentPage * projectsPerPage, 
    (currentPage + 1) * projectsPerPage
  );

  const handleFilterChange = (filterId) => {
    setActiveFilter(filterId);
    setCurrentPage(0);
  };

  const nextPage = () => {
    setCurrentPage((prev) => (prev + 1) % totalPages);
  };

  const prevPage = () => {
    setCurrentPage((prev) => (prev - 1 + totalPages) % totalPages);
  };

  return (
    <section id="project-examples" className="py-20 px-4 bg-gradient-to-br from-gray-50 to-blue-50 relative overflow-hidden">
      {/* Background Decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 right-20 w-64 h-64 bg-purple-200/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 left-20 w-64 h-64 bg-blue-200/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-purple-100 text-purple-600 rounded-full font-semibold text-sm mb-4">
            <Filter className="w-4 h-4" />
            <span>Project Showcase</span>
          </div>
          
          <h2 className="text-4xl lg:text-5xl font-bold mb-6 bg-gradient-to-r from-purple-600 via-blue-600 to-teal-600 bg-clip-text text-transparent leading-tight">
            Our Recent Projects
          </h2>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Explore our portfolio of successful projects across different industries and technologies.
          </p>
        </div>

        {/* Filter Tabs */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {projectCategories.map((category) => (
            <button
              key={category.id}
              onClick={() => handleFilterChange(category.id)}
              className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                activeFilter === category.id
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'
                  : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-200'
              }`}
            >
              {category.name} ({category.count})
            </button>
          ))}
        </div>

        {/* Projects Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {currentProjects.map((project, index) => (
            <ProjectCard
              key={project.id}
              title={project.title}
              description={project.description}
              image={project.image}
              technologies={project.technologies}
              category={project.category}
              deliveryTime={project.deliveryTime}
              rating={project.rating}
              projectLink={project.projectLink}
              delay={`${index * 100}ms`}
              isVisible={isVisible}
            />
          ))}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-center gap-4">
            <button
              onClick={prevPage}
              className="p-3 bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 hover:border-blue-300"
            >
              <ChevronLeft className="w-5 h-5 text-gray-600" />
            </button>
            
            <div className="flex gap-2">
              {[...Array(totalPages)].map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentPage(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    currentPage === index
                      ? 'bg-blue-600'
                      : 'bg-gray-300 hover:bg-gray-400'
                  }`}
                />
              ))}
            </div>
            
            <button
              onClick={nextPage}
              className="p-3 bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 hover:border-blue-300"
            >
              <ChevronRight className="w-5 h-5 text-gray-600" />
            </button>
          </div>
        )}
      </div>
    </section>
  );
};

export default ProjectExamples;
