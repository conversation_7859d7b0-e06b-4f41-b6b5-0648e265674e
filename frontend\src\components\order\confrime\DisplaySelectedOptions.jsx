import React from 'react';
import { CheckCircle } from 'lucide-react'; // Icon used directly in this component

export const DisplaySelectedOptions = ({ label, items, noneSelectedText = "None selected", icon: IconComponent }) => {
  return (
    <div className="py-3 sm:grid sm:grid-cols-3 sm:gap-4 items-start">
      <dt className="text-sm font-medium text-slate-500 flex items-center">
        {IconComponent && <IconComponent size={16} className="mr-2 text-slate-400 flex-shrink-0" />}
        {label}:
      </dt>
      <dd className="mt-1 text-sm text-slate-900 sm:mt-0 sm:col-span-2">
        {(!items || items.length === 0) ? (
          <span className="italic">{noneSelectedText}</span>
        ) : (
          <ul className="space-y-1">
            {items.map((item, index) => (
              <li key={index} className="flex items-center">
                <CheckCircle size={14} className="mr-2 text-green-500 flex-shrink-0" />
                {item.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </li>
            ))}
          </ul>
        )}
      </dd>
    </div>
  );
};

export default DisplaySelectedOptions; // Also adding a default export for convenience