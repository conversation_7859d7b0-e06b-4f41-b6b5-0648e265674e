import React, { useState, useEffect } from 'react';
import { Menu, X, ChevronDown, Phone, Mail, Zap } from 'lucide-react';
import { Link, NavLink } from 'react-router-dom'; // Import Link and NavLink

export default function Navigation() {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navigationLinks = [
    { name: 'Home', href: '/' },
    { name: 'About Us', href: '/about' }, // Assuming /about route
    {
      name: 'Services',
      // href: '/services', // Main link if services page exists
      dropdown: [
        { name: 'Website Design', href: '/services#website-design' }, // Using hash links or full paths
        { name: 'E-commerce Development', href: '/services#ecommerce' },
        { name: 'Mobile Responsive', href: '/services#mobile' },
        { name: 'SEO Optimization', href: '/services#seo' },
        { name: 'Maintenance & Support', href: '/services#support' }
      ]
    },
    { name: 'Projects', href: '/projects' }, // Assuming /projects route
    { name: 'Contact', href: '/contact' }
  ];

  const toggleDropdown = (index) => {
    setActiveDropdown(activeDropdown === index ? null : index);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (activeDropdown !== null && !event.target.closest('.relative.group > .relative')) {
        setActiveDropdown(null);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [activeDropdown]);


  return (
    <>
      {/* Main Navigation */}
      <nav className={`fixed top-0 left-0 right-0 z-50 transition-all  duration-300 ${
        isScrolled
          ? 'bg-white/95 backdrop-blur-md shadow-lg ' // MODIFIED HERE
          : 'bg-white/90 backdrop-blur-sm'
      }`}>
        <div className="container mx-auto px-6">
          <div className="flex items-center justify-between h-20">

            {/* Logo */}
            <Link to="/" className="flex items-center space-x-3 cursor-pointer"> {/* Changed to Link */}
              <div className="bg-blue-600 rounded-xl p-2">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Chadoom</h1>
                <p className="text-xs text-blue-600 font-medium">24-Hour Delivery</p>
              </div>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-8">
              {navigationLinks.map((link, index) => (
                <div key={index} className="relative group">
                  {link.dropdown ? (
                    <div className="relative"> {/* Added relative for positioning context */}
                      <button
                        onClick={() => toggleDropdown(index)}
                        className="flex items-center space-x-1 text-gray-700 hover:text-blue-600 font-medium transition-colors duration-300 py-2 focus:outline-none"
                      >
                        <span>{link.name}</span>
                        <ChevronDown className={`w-4 h-4 transition-transform duration-300 ${
                          activeDropdown === index ? 'rotate-180' : ''
                        }`} />
                      </button>

                      <div className={`absolute top-full left-1/2 -translate-x-1/2 mt-2 w-64 bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden transition-all duration-300 origin-top ${
                        activeDropdown === index
                          ? 'opacity-100 scale-100 visible'
                          : 'opacity-0 scale-95 invisible'
                      }`}>
                        {link.dropdown.map((item, subIndex) => (
                          <Link // Changed to Link
                            key={subIndex}
                            to={item.href}
                            onClick={() => setActiveDropdown(null)} // Close dropdown on item click
                            className="block px-6 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 border-b border-gray-50 last:border-b-0"
                          >
                            {item.name}
                          </Link>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <NavLink // Changed to NavLink for active styling
                      to={link.href}
                      className={({ isActive }) =>
                        `text-gray-700 hover:text-blue-600 font-medium transition-colors duration-300 relative py-2 after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-blue-600 after:transition-all after:duration-300 hover:after:w-full ${
                          isActive ? 'text-blue-600 after:w-full' : ''
                        }`
                      }
                    >
                      {link.name}
                    </NavLink>
                  )}
                </div>
              ))}
            </div>

            {/* CTA Button & Mobile Menu Toggle */}
            <div className="flex items-center space-x-4">
              <Link to="/order" className="hidden lg:block bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg"> {/* Changed to Link */}
                Order Now
              </Link>

              <button
                onClick={() => setIsOpen(!isOpen)}
                className="lg:hidden p-2 text-gray-700 hover:text-blue-600 transition-colors duration-300 focus:outline-none"
                aria-label="Toggle menu"
              >
                {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        <div className={`lg:hidden transition-all ease-in-out duration-300 overflow-hidden ${
          isOpen ? 'max-h-[80vh] opacity-100 border-t border-gray-100' : 'max-h-0 opacity-0' // Added border-t here
        }`}>
          <div className="bg-white shadow-lg"> {/* Removed border-t from here */}
            <div className="container mx-auto px-6 py-4">

              <div className="bg-blue-50 rounded-xl p-4 mb-4">
                <div className="flex items-center justify-between text-sm text-blue-600">
                  <a href="tel:+15551234567" className="flex items-center hover:underline">
                    <Phone className="w-4 h-4 mr-2" />
                    <span>+****************</span>
                  </a>
                  <Link to="/contact" onClick={() => setIsOpen(false)} className="flex items-center hover:underline">
                    <Mail className="w-4 h-4 mr-2" />
                    <span>Email Us</span>
                  </Link>
                </div>
              </div>

              <div className="space-y-1">
                {navigationLinks.map((link, index) => (
                  <div key={index}>
                    {link.dropdown ? (
                      <div>
                        <button
                          onClick={() => toggleDropdown(index)}
                          className="flex items-center justify-between w-full text-left text-gray-700 hover:text-blue-600 font-medium py-3 px-4 rounded-xl hover:bg-blue-50 transition-all duration-300 focus:outline-none"
                        >
                          <span>{link.name}</span>
                          <ChevronDown className={`w-4 h-4 transition-transform duration-300 ${
                            activeDropdown === index ? 'rotate-180' : ''
                          }`} />
                        </button>

                        <div className={`overflow-hidden transition-all ease-in-out duration-300 ${
                          activeDropdown === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0' // Increased max-h
                        }`}>
                          <div className="ml-4 space-y-1 py-1">
                            {link.dropdown.map((item, subIndex) => (
                              <Link // Changed to Link
                                key={subIndex}
                                to={item.href}
                                onClick={() => { setIsOpen(false); setActiveDropdown(null); }} // Close all on click
                                className="block text-gray-600 hover:text-blue-600 py-2 px-4 rounded-lg hover:bg-blue-50 transition-colors duration-200"
                              >
                                {item.name}
                              </Link>
                            ))}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <NavLink // Changed to NavLink
                        to={link.href}
                        onClick={() => setIsOpen(false)} // Close menu on link click
                        className={({ isActive }) =>
                          `block font-medium py-3 px-4 rounded-xl hover:bg-blue-50 transition-all duration-300 ${
                            isActive ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:text-blue-600'
                          }`
                        }
                      >
                        {link.name}
                      </NavLink>
                    )}
                  </div>
                ))}
              </div>

              <div className="mt-6 pt-4 border-t border-gray-100">
                <Link to="/order" onClick={() => setIsOpen(false)} className="block w-full text-center bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105"> {/* Changed to Link */}
                  Order Now
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>
    </>
  );
}