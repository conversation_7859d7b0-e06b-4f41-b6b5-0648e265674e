import React, { useState, useEffect } from 'react';
import { <PERSON>Right, Zap, CheckCircle, Star, Globe, Smartphone, Palette } from 'lucide-react';

export default function HeroSection() {
  const [currentFeature, setCurrentFeature] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  const features = [
    { icon: Zap, text: "Lightning Fast Delivery" },
    { icon: Palette, text: "Perfect Design" },
    { icon: Smartphone, text: "Mobile Responsive" },
    { icon: Globe, text: "SEO Optimized" }
  ];

  useEffect(() => {
    setIsVisible(true);
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 2000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen  relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
        <div className="absolute top-40 right-10 w-96 h-96 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-1/4 right-1/4 animate-bounce delay-1000">
        <div className="w-8 h-8 bg-blue-500 rounded-lg rotate-45 opacity-60"></div>
      </div>
      <div className="absolute bottom-1/3 left-1/4 animate-bounce delay-2000">
        <div className="w-6 h-6 bg-blue-600 rounded-full opacity-50"></div>
      </div>

      <div className="relative z-10 container mx-auto px-6 py-6">
        <div className="flex flex-col lg:flex-row items-center justify-between min-h-screen">
          
          {/* Left Content */}
          <div className={`lg:w-1/2 mb-12 lg:mb-0 transform transition-all duration-1000 ${isVisible ? 'translate-x-0 opacity-100' : '-translate-x-20 opacity-0'}`}>
            
            {/* Badge */}
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6 animate-pulse">
              <Star className="w-4 h-4 mr-2 fill-current" />
              #1 Fastest Website Creation Service
            </div>

            {/* Main Heading */}
            <h1 className="text-5xl lg:text-7xl font-bold text-gray-900 leading-tight mb-6">
              Perfect 
              <span className="text-blue-600 block">Websites</span>
              <span className="text-2xl lg:text-3xl text-gray-600 font-normal">in just one day</span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl text-gray-600 mb-8 leading-relaxed max-w-lg">
              Transform your business with stunning, professional websites delivered in 24 hours. 
              No waiting, no hassle, just perfect results.
            </p>

            {/* Feature Highlight */}
            <div className="mb-8">
              <div className="flex items-center space-x-3 bg-white rounded-2xl px-6 py-4 shadow-lg border border-blue-100">
                {React.createElement(features[currentFeature].icon, { 
                  className: "w-6 h-6 text-blue-600 transition-all duration-500" 
                })}
                <span className="text-lg font-semibold text-gray-800 transition-all duration-500">
                  {features[currentFeature].text}
                </span>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 mb-8">
              <button className="group bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-2xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl flex items-center justify-center">
                Start Your Website Today
                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </button>
              <button className="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-4 rounded-2xl font-semibold text-lg transition-all duration-300 transform hover:scale-105">
                View Portfolio
              </button>
            </div>

            {/* Trust Indicators */}
            <div className="flex items-center space-x-6 text-sm text-gray-500">
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                Money-back guarantee
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                24/7 support
              </div>
            </div>
          </div>

          {/* Right Content - Visual Elements */}
          <div className={`lg:w-1/2 transform transition-all duration-1000 delay-300 ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-20 opacity-0'}`}>
            
            {/* Main Visual Container */}
            <div className="relative">
              
              {/* Central Device Mockup */}
              <div className="relative mx-auto w-80 h-96 bg-white rounded-3xl shadow-2xl border-8 border-gray-200 overflow-hidden transform rotate-3 hover:rotate-0 transition-transform duration-500">
                
                {/* Screen Content */}
                <div className="h-full bg-gradient-to-br from-blue-500 to-blue-700 p-6 flex flex-col">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex space-x-2">
                      <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                    </div>
                  </div>
                  
                  <div className="flex-1 bg-white rounded-xl p-4 shadow-inner">
                    <div className="space-y-3">
                      <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                      <div className="h-4 bg-blue-200 rounded animate-pulse delay-200"></div>
                      <div className="h-4 bg-gray-200 rounded animate-pulse delay-400"></div>
                      <div className="mt-4 h-20 bg-blue-900 rounded-lg animate-pulse delay-600"></div>
                      <div className="flex space-x-2">
                        <div className="h-8 flex-1 bg-blue-500 rounded animate-pulse delay-800"></div>
                        <div className="h-8 flex-1 bg-gray-200 rounded animate-pulse delay-1000"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating Stats */}
              <div className="absolute -top-6 -left-6 bg-white rounded-2xl px-4 py-3 shadow-lg border border-blue-100 animate-bounce">
                <div className="text-2xl font-bold text-blue-600">24h</div>
                <div className="text-xs text-gray-500">Delivery</div>
              </div>

              <div className="absolute -bottom-6 -right-6 bg-white rounded-2xl px-4 py-3 shadow-lg border border-green-100 animate-bounce delay-1000">
                <div className="text-2xl font-bold text-green-600">100%</div>
                <div className="text-xs text-gray-500">Satisfaction</div>
              </div>

              <div className="absolute top-1/2 -right-12 bg-white rounded-2xl px-4 py-3 shadow-lg border border-purple-100 animate-bounce delay-500">
                <div className="text-2xl font-bold text-purple-600">500+</div>
                <div className="text-xs text-gray-500">Happy Clients</div>
              </div>
            </div>
          </div>
        </div>

        
      </div>
    </div>
  );
}