import React from 'react';
// Import helpers from their individual files in the same 'confirm' folder
import { DisplaySelectedOptions } from './DisplaySelectedOptions';
import { DisplaySimpleValue } from './DisplaySimpleValue';
import { DisplayUploadedFile } from './DisplayUploadedFile';

// Import all necessary icons for this specific step
import {
  AlertCircle,
  FileText,
  ImageIcon,
  Palette,
  Link as LinkIcon,
  Edit3,
  DollarSign,
  ListChecks,
  ShieldCheck,
  User,
  Mail,
  Briefcase,
  Phone,
  MessageSquare,
  Target,
  Award,
  ClipboardList
} from 'lucide-react';

const ConfirmationStep = ({ formData, handleChange, errors }) => {
  const websiteTypeOptions = [
    { value: 'ecommerce', label: '🛒 E-commerce Store', description: 'Est: $2,999 - $4,999' },
    { value: 'personal', label: '👤 Personal Site', description: 'Est: $1,999 - $3,999' },
    { value: 'ai_website', label: '🤖 AI-Powered Website', description: 'Est: $4,999 - $9,999' },
    { value: 'custom', label: '✨ Custom Solution', description: 'We will contact you for pricing' },
  ];

  const selectedWebsiteType = websiteTypeOptions.find(
    (option) => option.value === formData.websiteType
  );

  const SectionTitle = ({ title }) => (
    <h3 className="text-lg leading-6 font-semibold text-indigo-700 border-b-2 border-indigo-200 pb-2 mb-4 mt-2 first:mt-0">
      {title}
    </h3>
  );

  return (
    <div className="space-y-8">
      <div className="text-center md:text-left">
        <h2 className="text-2xl md:text-3xl font-bold text-slate-800 mb-1">
          <ShieldCheck size={30} className="inline-block mr-2 text-green-600 align-middle" /> Step 4: Review & Confirm
        </h2>
        <p className="text-sm md:text-base text-slate-600">
          Please carefully review your project details below. If everything looks correct, agree to the terms and submit your request.
        </p>
      </div>

      <div className="bg-white shadow-xl rounded-xl overflow-hidden">
        <div className="px-4 py-5 sm:px-6 bg-slate-50 border-b border-slate-200">
          <h3 className="text-xl leading-6 font-bold text-slate-800">
            Project Order Summary
          </h3>
          <p className="mt-1 max-w-2xl text-sm text-slate-500">
            Your selections and provided information.
          </p>
        </div>
        <div className="border-t border-slate-200">
          <dl className="divide-y divide-slate-200">
            {/* Project Basics Section */}
            <div className="px-4 py-3 sm:px-6">
                <SectionTitle title="Client & Project Information" />
                <DisplaySimpleValue label="Full Name" value={formData.fullName} icon={User}/>
                <DisplaySimpleValue label="Email" value={formData.email} icon={Mail}/>
                <DisplaySimpleValue label="Business Name" value={formData.businessName} icon={Briefcase}/>
                <DisplaySimpleValue label="Phone Number" value={formData.phoneNumber} icon={Phone}/>
                {selectedWebsiteType && (
                    <>
                    <DisplaySimpleValue label="Website Type" value={selectedWebsiteType.label} icon={Palette} />
                    <DisplaySimpleValue label="Est. Cost / Note" value={selectedWebsiteType.description} icon={DollarSign} isCurrency={selectedWebsiteType.value !== 'custom'} />
                    </>
                )}
                <DisplaySimpleValue label="Project Description" value={formData.websiteDescription} icon={MessageSquare}/>
            </div>
            
            {/* Goals & Preferences Section */}
            <div className="px-4 py-3 sm:px-6">
                <SectionTitle title="Goals & Preferences" />
                <DisplaySelectedOptions label="Main Goals" items={formData.mainGoal || []} icon={Target} />
                {formData.mainGoal && formData.mainGoal.includes('other_goal') && (
                    <DisplaySimpleValue label="Other Specified Goal" value={formData.mainGoalOther} icon={Edit3}/>
                )}
                <DisplaySimpleValue 
                    label="Logo & Branding Status"
                    value={formData.logoBranding?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} 
                    icon={Award}
                />
                <DisplaySimpleValue 
                    label="Content Readiness"
                    value={formData.contentReady?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} 
                    icon={ClipboardList}
                />
            </div>

            {/* Uploaded Files Section */}
            <div className="px-4 py-3 sm:px-6">
                <SectionTitle title="Uploaded Assets" />
                <DisplayUploadedFile label="Logo File" fileName={formData.uploads?.logo?.name} icon={ImageIcon}/>
                <DisplayUploadedFile label="Inspirations/Samples" fileName={formData.uploads?.sampleSites?.name} icon={LinkIcon}/>
                <DisplayUploadedFile label="Brand Guidelines" fileName={formData.uploads?.brandColors?.name} icon={Palette}/>
                <DisplayUploadedFile label="Other Documents" fileName={formData.uploads?.documents?.name} icon={FileText}/>
            </div>
          </dl>
        </div>
      </div>

      <div className="mt-8 bg-white p-6 rounded-xl shadow-lg">
        <label htmlFor="agreedToTerms" className="flex items-start space-x-3 cursor-pointer group">
          <input
            type="checkbox"
            name="agreedToTerms"
            id="agreedToTerms"
            checked={!!formData.agreedToTerms}
            onChange={handleChange}
            className="h-5 w-5 mt-0.5 text-indigo-600 border-slate-300 rounded focus:ring-indigo-500 focus:ring-offset-2 group-hover:border-indigo-400 transition-colors"
          />
          <span className="text-sm text-slate-700 group-hover:text-indigo-700">
            I have reviewed all the information and agree to the <a href="/terms-of-service" target="_blank" rel="noopener noreferrer" className="font-medium text-indigo-600 hover:text-indigo-800 underline">Terms of Service</a> and <a href="/privacy-policy" target="_blank" rel="noopener noreferrer" className="font-medium text-indigo-600 hover:text-indigo-800 underline">Privacy Policy</a>.
          </span>
        </label>
        {errors?.agreedToTerms && (
            <p className="mt-2 text-xs text-red-600 flex items-center">
                <AlertCircle size={14} className="mr-1.5"/> {errors.agreedToTerms}
            </p>
        )}
      </div>
      <p className="text-xs text-slate-500 mt-6 text-center">
        By submitting this form, you are requesting a quote and consultation. No payment is required at this stage. We will contact you to discuss your project further.
      </p>
    </div>
  );
};

export default ConfirmationStep;