import React, { useState, useEffect } from 'react';
import { FileText, MessageCircle, Code, CheckCircle } from 'lucide-react';

const HowWeWork = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  const steps = [
    {
      icon: FileText,
      title: "Fill Out The Form",
      description: "Complete our detailed project form with your requirements, vision, and specific needs to get started.",
      color: "from-blue-500 to-blue-600",
      hoverColor: "hover:shadow-blue-500/20",
      borderColor: "hover:border-blue-400/60",
      delay: "0ms"
    },
    {
      icon: MessageCircle,
      title: "We Contact You",
      description: "Our team reaches out within 24 hours to discuss your project details and answer any questions.",
      color: "from-blue-600 to-blue-700",
      hoverColor: "hover:shadow-blue-600/20",
      borderColor: "hover:border-blue-500/60",
      delay: "150ms"
    },
    {
      icon: Code,
      title: "Create Your Website",
      description: "We design and develop your custom website using the latest technologies and best practices.",
      color: "from-blue-700 to-blue-800",
      hoverColor: "hover:shadow-blue-700/20",
      borderColor: "hover:border-blue-600/60",
      delay: "300ms"
    },
    {
      icon: CheckCircle,
      title: "We Deliver It Ready",
      description: "Your fully tested, optimized website is delivered ready to launch with ongoing support included.",
      color: "from-blue-800 to-blue-900",
      hoverColor: "hover:shadow-blue-800/20",
      borderColor: "hover:border-blue-700/60",
      delay: "450ms"
    }
  ];

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 bg-clip-text text-transparent">
            How We Work
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Our streamlined process ensures your project is completed efficiently and exceeds your expectations
          </p>
        </div>

        {/* Process Cards */}
        <div className="grid md:grid-cols-2 xl:grid-cols-4 gap-8">
          {steps.map((step, index) => (
            <div
              key={index}
              className={`group relative bg-white p-8 rounded-3xl shadow-lg ${step.hoverColor} transform hover:-translate-y-6 transition-all duration-700 border-2 border-gray-100 ${step.borderColor} ${
                isVisible ? 'animate-in slide-in-from-bottom-8 fade-in' : 'opacity-0 translate-y-8'
              }`}
              style={{ 
                animationDelay: step.delay,
                animationDuration: '800ms',
                animationFillMode: 'forwards'
              }}
            >
              {/* Step Number */}
              <div className="absolute -top-4 -right-4 w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-lg">{index + 1}</span>
              </div>

              {/* Icon */}
              <div className={`w-24 h-24 mx-auto mb-6 rounded-2xl bg-gradient-to-r ${step.color} flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg`}>
                <step.icon className="w-12 h-12 text-white" />
              </div>

              {/* Content */}
              <div className="text-center">
                <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-blue-700 transition-colors duration-300">
                  {step.title}
                </h3>
                <p className="text-gray-600 leading-relaxed text-base">
                  {step.description}
                </p>
              </div>

              {/* Hover Effect Overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-blue-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
              
              {/* Bottom Accent Line */}
              <div className={`absolute bottom-0 left-8 right-8 h-1 bg-gradient-to-r ${step.color} opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-full`}></div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default HowWeWork;