import React from 'react';
import { Upload, CheckCircle, Clock, AlertTriangle } from 'lucide-react'; // Keep only used icons

export const FileUploadField = ({ label, name, fileName, onChange, helpText, error, icon: Icon, priority }) => {
  const handleNativeFileChange = (e) => {
    const file = e.target.files[0];
    if (file && onChange) {
      // Call the onChange prop with (fieldName, fileObject)
      // 'name' is a prop passed to FileUploadField (e.g., "logo", "sampleSites")
      onChange(name, file);
    }
     // Reset input value to allow re-uploading the same file if needed
     e.target.value = null;
  };

  const isUploaded = !!fileName;
  const hasError = !!error;
  const uniqueId = `file-input-${name.replace(/\s+/g, '-').toLowerCase()}`;


  return (
    <div className={`relative group ${priority === 'high' ? 'order-1' : priority === 'medium' ? 'order-2' : 'order-3'}`}>
      {priority === 'high' && (
        <div className="absolute -top-2 -right-2 z-10">
          <div className="bg-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
            Recommended
          </div>
        </div>
      )}

      <div
        className={`
          relative border-2 border-dashed rounded-2xl p-6 transition-all duration-300
          bg-gradient-to-br from-white to-slate-50 hover:from-slate-50 hover:to-slate-100
          ${hasError
            ? 'border-red-300 bg-gradient-to-br from-red-50 to-red-100 shadow-red-100'
            : isUploaded
              ? 'border-emerald-300 bg-gradient-to-br from-emerald-50 to-emerald-100 shadow-emerald-100'
              : 'border-slate-300 hover:border-slate-400'
          }
          shadow-lg hover:shadow-xl group-hover:scale-[1.02] cursor-pointer
          ${priority === 'high' ? 'ring-2 ring-blue-200 ring-opacity-50' : ''}
        `}
        onClick={() => document.getElementById(uniqueId)?.click()}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') {e.preventDefault(); document.getElementById(uniqueId)?.click();}}}
      >
        <input
          type="file"
          id={uniqueId}
          name={name}
          onChange={handleNativeFileChange}
          className="sr-only" // Visually hidden but accessible
        />

        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center min-w-0"> {/* Added min-w-0 for flex child truncation */}
            <div className={`
              w-12 h-12 rounded-xl flex items-center justify-center mr-3 transition-all duration-300 flex-shrink-0
              ${hasError ? 'bg-red-200 text-red-600' : isUploaded ? 'bg-emerald-200 text-emerald-600' : 'bg-slate-200 text-slate-600 group-hover:bg-slate-300'}
            `}>
              {Icon && <Icon size={24} />}
            </div>
            <div className="min-w-0"> {/* Added min-w-0 for flex child truncation */}
              <h3 className="font-semibold text-slate-800 text-sm leading-tight truncate">{label}</h3>
              {priority === 'high' && <span className="text-xs text-orange-600 font-medium">Highly recommended</span>}
            </div>
          </div>
          <div className="flex-shrink-0 ml-2"> {/* Added ml-2 for spacing */}
            {isUploaded ? <CheckCircle className="w-6 h-6 text-emerald-500" /> : hasError ? <AlertTriangle className="w-6 h-6 text-red-500" /> : <Clock className="w-6 h-6 text-slate-400" />}
          </div>
        </div>

        <div className="text-center py-4 min-h-[100px] flex flex-col justify-center">
          {isUploaded ? (
            <div>
              <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <CheckCircle className="w-8 h-8 text-emerald-600" />
              </div>
              <p className="font-semibold text-emerald-700 text-sm mb-1 truncate max-w-[200px] mx-auto px-1">{fileName}</p>
              <p className="text-xs text-emerald-600">Successfully uploaded</p>
            </div>
          ) : (
            <div className="pointer-events-none">
              <div className={`
                w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3 transition-all duration-300
                ${hasError ? 'bg-red-100 text-red-500' : 'bg-slate-100 text-slate-500 group-hover:bg-slate-200'}
              `}>
                <Upload className="w-8 h-8" />
              </div>
              <p className="font-medium text-slate-700 text-sm mb-1">
                Click to browse or drag & drop
              </p>
              <p className="text-xs text-slate-500">{helpText}</p>
            </div>
          )}
        </div>

        {hasError && (
          <div className="mt-4 p-3 bg-red-100 border border-red-200 rounded-lg">
            <p className="text-xs text-red-600 flex items-center">
              <AlertTriangle className="w-3 h-3 mr-1 flex-shrink-0" />
              {error}
            </p>
          </div>
        )}

        <div className="mt-4">
          <div className={`
            w-full py-2 px-4 rounded-lg border-2 border-dashed text-center text-sm font-medium transition-all duration-300 pointer-events-none
            ${isUploaded ? 'border-emerald-300 text-emerald-600 bg-emerald-50' : hasError ? 'border-red-300 text-red-600 bg-red-50' : 'border-slate-300 text-slate-600 bg-white hover:border-slate-400 hover:bg-slate-50'}
          `}>
            {isUploaded ? 'Change File' : 'Choose File'}
          </div>
        </div>
      </div>
    </div>
  );
};