import React from 'react';
import { InputField } from './form/InputField';       // Import from InputField.jsx
import { RadioGroup } from './form/RadioGroup';      // Import from RadioGroup.jsx
import { CheckboxGroup } from './form/CheckboxGroup'; // Import from CheckboxGroup.jsx

const Step2_GoalsPreferences = ({ formData, handleChange, errors }) => {
  // ... rest of your component code remains the same
  const mainGoalOptions = [
    { value: 'sell_products', label: '💰 Sell Products' },
    { value: 'get_bookings', label: '📅 Get Bookings' },
    { value: 'build_brand', label: '🌟 Build Personal Brand' },
    { value: 'informational', label: 'ℹ️ Informational Website' },
    { value: 'other_goal', label: '✍️ Other' },
  ];

  const logoBrandingOptions = [
    { value: 'yes_have_logo', label: 'Yes, I have them' },
    { value: 'no_need_help_logo', label: 'No, I need design help' },
  ];

  const contentReadyOptions = [
    { value: 'yes_content_ready', label: 'Yes, all text & images are ready' },
    { value: 'no_need_help_content', label: 'No, I need help with content' },
  ];

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold text-slate-800 mb-1">🥈 Step 2: Goals & Preferences</h2>
      <p className="text-sm text-slate-600 mb-6">Help us understand what you want to achieve with your new website.</p>

      <CheckboxGroup
        label="What is your main goal for this website? (Select all that apply)"
        name="mainGoal"
        options={mainGoalOptions}
        selectedOptions={formData.mainGoal || []} // Ensure selectedOptions is always an array
        onChange={handleChange}
        error={errors.mainGoal}
      />

      {/* Ensure formData.mainGoal exists before calling .includes */}
      {formData.mainGoal && formData.mainGoal.includes('other_goal') && (
        <InputField
          label="Please specify your other goal"
          name="mainGoalOther"
          value={formData.mainGoalOther}
          onChange={handleChange}
          placeholder="e.g., Showcase a portfolio"
          error={errors.mainGoalOther}
        />
      )}

      <RadioGroup
        label="Do you have a logo and branding guidelines ready?"
        name="logoBranding"
        options={logoBrandingOptions}
        value={formData.logoBranding}
        onChange={handleChange}
        error={errors.logoBranding}
      />

      <RadioGroup
        label="Do you have content (text/images) ready for the website?"
        name="contentReady"
        options={contentReadyOptions}
        value={formData.contentReady}
        onChange={handleChange}
        error={errors.contentReady}
      />
    </div>
  );
};

export default Step2_GoalsPreferences;