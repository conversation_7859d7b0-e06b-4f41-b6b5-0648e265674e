import React, { useState } from 'react';
import { Eye, EyeOff, Check, AlertCircle, Info } from 'lucide-react'; // Import necessary icons

export const InputField = ({
  label,
  name,
  type = "text",
  value,
  onChange,
  placeholder,
  error,
  optional,
  disabled,
  helpText,
  icon: IconComponent // Renamed to avoid conflict with Icon from lucide-react if used directly
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  const isPassword = type === 'password';
  const currentInputType = isPassword && showPassword ? 'text' : type;

  let prClass = 'pr-4'; // Default padding right
  if (isPassword) {
    prClass = 'pr-12'; // Space for eye icon
  } else if (value && !error && !disabled && type !== 'email' && type !== 'tel') { // Avoid checkmark for email/tel where format validation is key
    prClass = 'pr-10'; // Space for check icon
  }


  return (
    <div className="group w-full"> {/* Ensure it takes full width if in a grid */}
      <div className="flex items-center justify-between mb-1.5"> {/* Reduced mb slightly */}
        <label htmlFor={name} className="flex items-center text-sm font-semibold text-slate-700">
          {IconComponent && <IconComponent size={16} className="mr-2 text-slate-500 flex-shrink-0" />}
          {label}
          {optional && (
            <span className="ml-2 text-xs font-normal text-slate-500 bg-slate-100 px-1.5 py-0.5 rounded-full">
              Optional
            </span>
          )}
        </label>
        {helpText && (
          <div className="relative group/tooltip">
            <Info size={16} className="text-slate-400 cursor-help hover:text-slate-600" />
            <div className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 w-max max-w-xs p-2.5 bg-slate-800 text-white text-xs rounded-lg shadow-lg opacity-0 pointer-events-none group-hover/tooltip:opacity-100 transition-opacity duration-200 z-20">
              {helpText}
            </div>
          </div>
        )}
      </div>

      <div className="relative">
        <input
          type={currentInputType}
          name={name}
          id={name}
          value={value || ''} // Ensure value is not undefined
          onChange={onChange}
          placeholder={placeholder}
          disabled={disabled}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          className={`
            w-full px-4 py-3 ${prClass}
            border-2 rounded-xl shadow-sm transition-all duration-300
            text-slate-800 placeholder-slate-400 text-sm
            focus:outline-none
            ${error
              ? 'border-red-400 bg-red-50 focus:border-red-500 focus:ring-4 focus:ring-red-600/20'
              : isFocused
                ? 'border-blue-500 bg-blue-50 focus:ring-4 focus:ring-blue-600/20'
                : 'border-slate-300 bg-white hover:border-slate-400 focus:border-blue-500'
            }
            ${disabled
              ? 'bg-slate-100 text-slate-500 cursor-not-allowed hover:border-slate-300'
              : ''
            }
            ${value && !isFocused ? 'font-medium' : ''}
          `}
        />

        {isPassword && (
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 p-1 focus:outline-none rounded-md focus:bg-slate-100"
            aria-label={showPassword ? "Hide password" : "Show password"}
          >
            {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
          </button>
        )}

        {/* Simple success indicator (check mark) */}
        {value && !error && !disabled && !isPassword && type !== 'email' && type !== 'tel' && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500">
            <Check size={20} />
          </div>
        )}
      </div>

      {error && (
        <div className="mt-1.5 flex items-center text-red-600 text-xs">
          <AlertCircle size={14} className="mr-1 flex-shrink-0" />
          {error}
        </div>
      )}
    </div>
  );
};