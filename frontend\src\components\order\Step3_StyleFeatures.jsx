import React from 'react';
// Corrected imports:
import { InputField } from './form/InputField';
import { CheckboxGroup } from './form/CheckboxGroup';

const Step3_StyleFeatures = ({ formData, handleChange, errors }) => {
  const styleOptions = [
    { value: 'clean_minimal', label: '✨ Clean & Minimal' },
    { value: 'bold_colorful', label: '🎨 Bold & Colorful' },
    { value: 'corporate_professional', label: '👔 Corporate & Professional' },
    { value: 'trendy_animated', label: '🚀 Trendy & Animated' },
  ];

  const featureOptions = [
    { value: 'contact_form', label: '✉️ Contact Form' },
    { value: 'booking_system', label: '🗓️ Booking System' },
    { value: 'payment_gateway', label: '💳 Payment Gateway' },
    { value: 'testimonials', label: '💬 Testimonials Section' },
    { value: 'blog', label: '📝 Blog / News Section' },
    { value: 'multi_language', label: '🌐 Multi-language Support' },
    { value: 'other_feature', label: '➕ Other Specific Feature' },
  ];

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold text-slate-800 mb-1">🥉 Step 3: Style & Features</h2>
      <p className="text-sm text-slate-600 mb-6">Let's define the look, feel, and functionality of your website.</p>

      <CheckboxGroup
        label="Choose a design style you prefer (Select all that apply)"
        name="stylePreferences"
        options={styleOptions}
        selectedOptions={formData.stylePreferences || []} // Ensure it's always an array
        onChange={handleChange}
        error={errors.stylePreferences}
      />

     

      {/* Ensure formData.featuresNeeded exists and is an array before calling .includes */}
      {formData.featuresNeeded && Array.isArray(formData.featuresNeeded) && formData.featuresNeeded.includes('other_feature') && (
        <InputField
          label="Please specify other features you need"
          name="featuresNeededOther"
          value={formData.featuresNeededOther}
          onChange={handleChange}
          placeholder="e.g., Interactive map, User accounts"
          error={errors.featuresNeededOther}
        />
      )}
    </div>
  );
};

export default Step3_StyleFeatures;