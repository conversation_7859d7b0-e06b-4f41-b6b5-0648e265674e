import React, { useState, useEffect } from 'react';
import { Smartphone, Globe, Code, Zap, Shield, Rocket } from 'lucide-react';
import ServiceCard from './ServiceCard';

const MainServices = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 200);
    return () => clearTimeout(timer);
  }, []);

  const mainServices = [
    {
      icon: Smartphone,
      title: "Mobile Application Development",
      description: "Create powerful, user-friendly mobile apps for iOS and Android platforms with cutting-edge technology and seamless user experience.",
      features: [
        "Cross-Platform Development",
        "Native Performance",
        "App Store Optimization",
        "Push Notifications",
        "Offline Functionality"
      ],
      color: "from-blue-500 to-cyan-500",
      bgColor: "from-blue-50 to-cyan-50",
      hoverColor: "hover:shadow-blue-500/25",
      borderColor: "hover:border-blue-400/60",
      delay: "0ms",
      price: "Starting at $4,999",
      contactLink: "/contact",
      viewServicesLink: "#mobile-projects"
    },
    {
      icon: Globe,
      title: "Full-Stack Website Development",
      description: "Complete web solutions from frontend to backend, including databases, APIs, and server management for scalable business applications.",
      features: [
        "Frontend & Backend",
        "Database Design",
        "API Development",
        "Cloud Deployment",
        "Security Implementation"
      ],
      color: "from-purple-500 to-pink-500",
      bgColor: "from-purple-50 to-pink-50",
      hoverColor: "hover:shadow-purple-500/25",
      borderColor: "hover:border-purple-400/60",
      delay: "200ms",
      price: "Starting at $3,999",
      contactLink: "/contact",
      viewServicesLink: "#fullstack-projects"
    },
    {
      icon: Code,
      title: "Frontend Website Development",
      description: "Beautiful, responsive, and interactive user interfaces that provide exceptional user experiences across all devices and browsers.",
      features: [
        "Responsive Design",
        "Modern Frameworks",
        "Performance Optimization",
        "SEO Friendly",
        "Cross-Browser Compatible"
      ],
      color: "from-green-500 to-teal-500",
      bgColor: "from-green-50 to-teal-50",
      hoverColor: "hover:shadow-green-500/25",
      borderColor: "hover:border-green-400/60",
      delay: "400ms",
      price: "Starting at $2,499",
      contactLink: "/contact",
      viewServicesLink: "#frontend-projects"
    }
  ];

  return (
    <section className="py-20 px-4 relative overflow-hidden">
      {/* Background Decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-200/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-200/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-blue-100/10 to-purple-100/10 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-100 text-blue-600 rounded-full font-semibold text-sm mb-4">
            <Zap className="w-4 h-4" />
            <span>Our Core Services</span>
          </div>
          
          <h2 className="text-4xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 bg-clip-text text-transparent leading-tight">
            Professional Development Services
          </h2>
          
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Transform your digital vision into reality with our comprehensive development services. 
            From mobile apps to full-stack web solutions, we deliver excellence with every project.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          {mainServices.map((service, index) => (
            <ServiceCard
              key={index}
              icon={service.icon}
              title={service.title}
              description={service.description}
              features={service.features}
              color={service.color}
              bgColor={service.bgColor}
              hoverColor={service.hoverColor}
              borderColor={service.borderColor}
              price={service.price}
              delay={service.delay}
              contactLink={service.contactLink}
              viewServicesLink={service.viewServicesLink}
              isVisible={isVisible}
            />
          ))}
        </div>

        {/* Additional Features Section */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-gray-100">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-800 mb-4">Why Choose Our Services?</h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              We combine technical expertise with creative innovation to deliver solutions that exceed expectations.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center p-6 rounded-2xl bg-gradient-to-br from-blue-50 to-cyan-50 border border-blue-100">
              <Shield className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <h4 className="text-lg font-semibold text-gray-800 mb-2">Secure & Reliable</h4>
              <p className="text-gray-600 text-sm">Enterprise-grade security and reliability in every solution we deliver.</p>
            </div>

            <div className="text-center p-6 rounded-2xl bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-100">
              <Rocket className="w-12 h-12 text-purple-600 mx-auto mb-4" />
              <h4 className="text-lg font-semibold text-gray-800 mb-2">Fast Delivery</h4>
              <p className="text-gray-600 text-sm">Quick turnaround times without compromising on quality or functionality.</p>
            </div>

            <div className="text-center p-6 rounded-2xl bg-gradient-to-br from-green-50 to-teal-50 border border-green-100">
              <Zap className="w-12 h-12 text-green-600 mx-auto mb-4" />
              <h4 className="text-lg font-semibold text-gray-800 mb-2">24/7 Support</h4>
              <p className="text-gray-600 text-sm">Round-the-clock support and maintenance for all our delivered projects.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default MainServices;
