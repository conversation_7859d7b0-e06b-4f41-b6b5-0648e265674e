import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Homepage from './pages/Home/Homepage';
import ContactPage from './pages/Contact/ContactPage';
import Navigation from './common/Navigation';
import OrderPage from './pages/order/OrderPage';
import Footer from './common/Footer';

const App = () => {
  return (
    // The main background can be applied to a wrapper div if needed,
    // or to individual page layouts for more control.
    // For this example, let's assume Navbar and Footer are part of a layout.
    <Router>
      <div className="flex flex-col min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        <Navigation /> {/* Shared Navbar outside of Routes if it's on every page */}
        <main className="flex-grow">
          <Routes>
            <Route path="/" element={<Homepage />} />
            <Route path="/contact" element={<ContactPage />} />
            <Route path="/Order" element={<OrderPage />} />

           </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  );
};

export default App;