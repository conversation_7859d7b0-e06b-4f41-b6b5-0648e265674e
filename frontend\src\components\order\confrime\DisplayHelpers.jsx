import React from 'react';
import { CheckCircle, FileText } from 'lucide-react'; // Minimal icons needed for these helpers directly

// Helper to display selected array options
export const DisplaySelectedOptions = ({ label, items, noneSelectedText = "None selected", icon: IconComponent }) => {
  return (
    <div className="py-3 sm:grid sm:grid-cols-3 sm:gap-4 items-start">
      <dt className="text-sm font-medium text-slate-500 flex items-center">
        {IconComponent && <IconComponent size={16} className="mr-2 text-slate-400 flex-shrink-0" />}
        {label}:
      </dt>
      <dd className="mt-1 text-sm text-slate-900 sm:mt-0 sm:col-span-2">
        {(!items || items.length === 0) ? (
          <span className="italic">{noneSelectedText}</span>
        ) : (
          <ul className="space-y-1">
            {items.map((item, index) => (
              <li key={index} className="flex items-center">
                <CheckCircle size={14} className="mr-2 text-green-500 flex-shrink-0" />
                {item.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </li>
            ))}
          </ul>
        )}
      </dd>
    </div>
  );
};

// Enhanced DisplaySimpleValue
export const DisplaySimpleValue = ({ label, value, placeholder = "Not provided", icon: IconComponent, isCurrency = false }) => (
  <div className="py-3 sm:grid sm:grid-cols-3 sm:gap-4 items-start">
    <dt className="text-sm font-medium text-slate-500 flex items-center">
      {IconComponent && <IconComponent size={16} className="mr-2 text-slate-400 flex-shrink-0" />}
      {label}:
    </dt>
    <dd className="mt-1 text-sm text-slate-900 sm:mt-0 sm:col-span-2 break-words">
      {value ? (
        isCurrency ? <span className="font-semibold text-indigo-600">{value}</span> : value
      ) : (
        <span className="italic text-slate-500">{placeholder}</span>
      )}
    </dd>
  </div>
);

// Component for displaying uploaded file names
export const DisplayUploadedFile = ({ label, fileName, icon: IconComponent }) => (
    <div className="py-2 sm:grid sm:grid-cols-3 sm:gap-4 items-start">
        <dt className="text-sm font-medium text-slate-500 flex items-center">
            {IconComponent && <IconComponent size={16} className="mr-2 text-slate-400 flex-shrink-0"/>}
            {label}:
        </dt>
        <dd className="mt-1 text-sm text-slate-900 sm:mt-0 sm:col-span-2 break-words">
            {fileName ? (
                <span className="flex items-center">
                    <CheckCircle size={14} className="mr-2 text-green-500 flex-shrink-0" />
                    {fileName}
                </span>
            ) : (
                <span className="italic text-slate-500">No file uploaded</span>
            )}
        </dd>
    </div>
);